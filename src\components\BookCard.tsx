
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Book, useCart } from '@/contexts/CartContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Eye, Heart, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface BookCardProps {
  book: Book;
}

const BookCard: React.FC<BookCardProps> = ({ book }) => {
  const { addToCart } = useCart();
  const { t, language } = useLanguage();
  const { theme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  // Return null if book is null or undefined
  if (!book) {
    return null;
  }

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(book, 1);
  };

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  // Safely determine the category path
  const category = book.category || 'books';
  const categoryPath = category.toLowerCase() === 'quran' ? 'quran' : 'books';

  // Format rating display
  const rating = book.rating || 0;
  const displayRating = rating.toFixed(1);

  return (
    <div
      className={`book-card rounded-xl overflow-hidden transition-all duration-300
        ${theme === 'light'
          ? 'bg-white border border-[#6e191c]/10 shadow-[0_5px_15px_rgba(110,25,28,0.05)] hover:shadow-[0_10px_25px_rgba(110,25,28,0.1)] hover:border-[#6e191c]/20'
          : 'bg-[#222] border border-[#6e191c]/10 shadow-[0_5px_15px_rgba(0,0,0,0.2)] hover:shadow-[0_10px_25px_rgba(110,25,28,0.15)] hover:border-[#6e191c]/20'
        } group animate-fade-in`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link to={`/${categoryPath}/${book.id}`} className="block h-full">
        <div className="relative h-[280px] overflow-hidden">
          {/* Enhanced gradient overlay for better depth */}
          <div className={`absolute inset-0 bg-gradient-to-t z-10
            ${theme === 'light'
              ? 'from-white/30 to-transparent'
              : 'from-[#1a1a1a]/40 to-transparent'
            }`}
          ></div>

          {/* Book Cover Image with enhanced transitions */}
          <img
            src={book.coverImage}
            alt={book.title || 'Book cover'}
            className={`book-card-image w-full h-full object-cover transition-all duration-700
              ${isHovered ? 'scale-110 filter brightness-105' : 'scale-100'}`}
            loading="lazy"
            onError={(e) => {
              // Fallback for broken image links
              (e.target as HTMLImageElement).src = 'https://placehold.co/400x600?text=No+Image';
            }}
          />

          {/* Enhanced Badges with better styling */}
          <div className="absolute top-3 left-3 flex flex-col gap-2 z-20">
            {book.bestSeller && (
              <Badge variant="secondary" className={`px-2.5 py-1 text-xs font-medium rounded-md
                ${theme === 'light'
                  ? 'bg-[#6e191c] text-white shadow-[0_2px_10px_rgba(110,25,28,0.2)]'
                  : 'bg-[#6e191c] text-white shadow-[0_2px_10px_rgba(0,0,0,0.3)]'}`}
              >
                {t('bestSeller')}
              </Badge>
            )}
            {book.newArrival && (
              <Badge variant="secondary" className={`px-2.5 py-1 text-xs font-medium rounded-md
                ${theme === 'light'
                  ? 'bg-white/90 border border-[#6e191c]/30 text-[#6e191c] shadow-[0_2px_10px_rgba(110,25,28,0.1)]'
                  : 'bg-[#222]/90 border border-[#6e191c]/30 text-white shadow-[0_2px_10px_rgba(0,0,0,0.2)]'}`}
              >
                {t('new')}
              </Badge>
            )}
          </div>

          {/* Enhanced Favorite Button with better styling */}
          <button
            onClick={toggleFavorite}
            className={`absolute top-3 right-3 p-2 rounded-full z-20 transition-all duration-300
              ${theme === 'light'
                ? 'bg-white shadow-[0_2px_10px_rgba(110,25,28,0.1)] text-[#6e191c] hover:bg-[#6e191c]/5'
                : 'bg-[#222] shadow-[0_2px_10px_rgba(0,0,0,0.2)] text-white hover:bg-[#6e191c]/10'
              } ${isHovered ? 'opacity-100 scale-100' : 'opacity-80 scale-95'}`}
            aria-label={isFavorite ? t('removeFromFavorites') : t('addToFavorites')}
          >
            <Heart className={`h-4 w-4 transition-all duration-300
              ${isFavorite
                ? 'fill-current text-[#6e191c]'
                : 'text-[#6e191c]/70 group-hover:text-[#6e191c]'}`}
            />
          </button>

          {/* Enhanced Rating with better styling */}
          {rating > 0 && (
            <div className={`absolute bottom-3 left-3 flex items-center gap-1 px-2.5 py-1.5 rounded-md z-20
              ${theme === 'light'
                ? 'bg-white/90 text-[#6e191c] shadow-[0_2px_10px_rgba(110,25,28,0.1)]'
                : 'bg-[#222]/90 text-white shadow-[0_2px_10px_rgba(0,0,0,0.2)]'
              } text-xs font-medium`}
            >
              <Star className="h-3.5 w-3.5 fill-current text-[#6e191c]" />
              <span>{displayRating}</span>
            </div>
          )}

          {/* Enhanced Quick Action Overlay with better styling */}
          <div className={`absolute inset-0 bg-gradient-to-t z-10
            ${theme === 'light'
              ? 'from-white/90 via-white/50 to-transparent'
              : 'from-[#1a1a1a]/90 via-[#1a1a1a]/50 to-transparent'
            } flex items-end justify-center p-5 opacity-0 group-hover:opacity-100 transition-all duration-300`}
          >
            <Button
              onClick={handleAddToCart}
              size="sm"
              className={`w-full py-2.5 rounded-md transition-all duration-300
                ${theme === 'light'
                  ? 'bg-[#6e191c] text-white hover:bg-[#8a1e24] shadow-[0_5px_15px_rgba(110,25,28,0.2)]'
                  : 'bg-[#6e191c] text-white hover:bg-[#8a1e24] shadow-[0_5px_15px_rgba(0,0,0,0.3)]'}`}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {t('addToCart')}
            </Button>
          </div>
        </div>

        {/* Enhanced Book Details with better typography and spacing */}
        <div className="p-5">
          <h3 className={`font-serif text-lg font-bold mb-2 line-clamp-2
            ${language === 'ar' ? 'text-right' : ''}
            ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}
          >
            {book.title || 'Untitled Book'}
          </h3>

          <p className={`text-sm mb-3 line-clamp-1
            ${language === 'ar' ? 'text-right' : ''}
            ${theme === 'light' ? 'text-black/70' : 'text-white/70'}`}
          >
            {book.author || 'Unknown Author'}
          </p>

          <div className={`flex items-center justify-between
            ${language === 'ar' ? 'flex-row-reverse' : ''}`}
          >
            <span className={`font-bold text-lg
              ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}
            >
              {(book.price || 0).toFixed(2)} TND
            </span>

            <span className={`flex items-center text-xs font-medium transition-all duration-300
              ${theme === 'light'
                ? 'text-[#6e191c]/70 hover:text-[#6e191c]'
                : 'text-white/70 hover:text-white'
              } cursor-pointer group-hover:translate-x-0.5`}
            >
              <Eye className="h-3.5 w-3.5 mr-1.5" />
              {t('details')}
            </span>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default BookCard;
