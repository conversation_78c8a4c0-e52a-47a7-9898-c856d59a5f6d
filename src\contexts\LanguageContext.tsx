
import React, { createContext, useState, useContext, ReactNode } from 'react';

type Language = 'en' | 'fr' | 'ar';

type LanguageContextType = {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
};

const translations = {
  en: {
    'home': 'Home',
    'books': 'Books',
    'quran': 'Quran',
    'cart': 'Cart',
    'contact': 'Contact Us',
    'account': 'Account',
    'search': 'Search',
    'featured': 'Featured Books',
    'newArrivals': 'New Arrivals',
    'bestSellers': 'Best Sellers',
    'quranCollections': 'Quran Collections',
    'islamicBooks': 'Islamic Books',
    'addToCart': 'Add to Cart',
    'viewDetails': 'View Details',
    'contactUs': 'Contact Us',
    'about': 'About Us',
    'address': 'Msaken, Sousse, Tunisia',
    'copyright': '© 2025 Kotobcom. All rights reserved.',
    'subscribeSuccess': 'Successfully subscribed!',
    'subscribeMessage': 'Thank you for subscribing to our newsletter.',
    'invalidEmail': 'Please enter a valid email address.',
    'shop': 'Shop',
    'customerService': 'Customer Service',
    'connectWithUs': 'Connect With Us',
    'paymentMethods': 'Payment Methods',
    'cashOnDelivery': 'Cash on Delivery',
    'myCart': 'My Cart',
    'shippingPolicy': 'Shipping Policy',
    'faq': 'FAQ',
    'privacyPolicy': 'Privacy Policy',
    'termsOfService': 'Terms of Service',
    'sitemap': 'Sitemap',
    'allRightsReserved': 'All rights reserved',
    'businessDays': 'business days',
    'estimatedDelivery': 'Estimated Delivery',
    'standardDelivery': 'Standard Delivery',
    'expressDelivery': 'Express Delivery',
    'orderDetails': 'Order Details',
    'shippingMethod': 'Shipping Method',
    'paymentMethod': 'Payment Method',
    'price': 'Price',
    'language': 'Language',
    'category': 'Category',
    'author': 'Author',
    'publisher': 'Publisher',
    'quantity': 'Quantity',
    'total': 'Total',
    'checkout': 'Checkout',
    'continueShopping': 'Continue Shopping',
    'removeItem': 'Remove',
    'emptyCart': 'Your cart is empty',
    'description': 'Description',
    'inStock': 'In Stock',
    'outOfStock': 'Out of Stock',
    'relatedItems': 'Related Items',
    'sendMessage': 'Send Message',
    'yourName': 'Your Name',
    'yourEmail': 'Your Email',
    'message': 'Message',
    'submit': 'Submit',
    'allBooks': 'All Books',
    'allQuran': 'All Quran',
    'filterBy': 'Filter By',
    'sortBy': 'Sort By',
    'productDetails': 'Product Details',
    'updateCart': 'Update Cart',
    'back': 'Back',
    'details': 'Details',
    'shipping': 'Shipping',
    'share': 'Share',
    'save': 'Save',
    'viewAllBooks': 'View All Books',
    'viewAllQurans': 'View All Qurans',
    'bookNotFound': 'Book Not Found',
    'bookNotFoundDesc': 'Sorry, we couldn\'t find the book you\'re looking for.',
    'returnToBooks': 'Return to Books',
    'quranNotFound': 'Quran Not Found',
    'quranNotFoundDesc': 'Sorry, we couldn\'t find the Quran you\'re looking for.',
    'returnToQuran': 'Return to Quran',
    'relatedQurans': 'Related Qurans',
    'days': 'Days',
    'deliveryTime': 'Delivery Time',
    'shippingInfo': 'Shipping Information',
    'shippingInfoDesc': 'We ship all books with secure packaging to ensure they arrive in perfect condition.',
    'specialHandling': 'Special Handling',
    'quranSpecialHandlingDesc': 'The Quran is handled with special care and respect during packing and shipping.',
    'reverentPackaging': 'All our Qurans are wrapped with reverent care and shipped in protective packaging.',
    'translator': 'Translator',
    'holyQuran': 'Holy Quran'
  },
  fr: {
    'home': 'Accueil',
    'books': 'Livres',
    'quran': 'Coran',
    'cart': 'Panier',
    'contact': 'Contactez-nous',
    'account': 'Compte',
    'search': 'Rechercher',
    'featured': 'Livres en Vedette',
    'newArrivals': 'Nouveautés',
    'bestSellers': 'Meilleures Ventes',
    'quranCollections': 'Collections de Coran',
    'islamicBooks': 'Livres Islamiques',
    'addToCart': 'Ajouter au Panier',
    'viewDetails': 'Voir Détails',
    'contactUs': 'Contactez-nous',
    'about': 'À Propos',
    'address': 'Msaken, Sousse, Tunisie',
    'copyright': '© 2025 Kotobcom. Tous droits réservés.',
    'subscribeSuccess': 'Abonnement réussi !',
    'subscribeMessage': 'Merci de vous être abonné à notre newsletter.',
    'invalidEmail': 'Veuillez entrer une adresse e-mail valide.',
    'shop': 'Boutique',
    'customerService': 'Service Client',
    'connectWithUs': 'Connectez-vous avec nous',
    'paymentMethods': 'Méthodes de paiement',
    'cashOnDelivery': 'Paiement à la livraison',
    'myCart': 'Mon panier',
    'shippingPolicy': 'Politique d\'expédition',
    'faq': 'FAQ',
    'privacyPolicy': 'Politique de confidentialité',
    'termsOfService': 'Conditions d\'utilisation',
    'sitemap': 'Plan du site',
    'allRightsReserved': 'Tous droits réservés',
    'businessDays': 'jours ouvrables',
    'estimatedDelivery': 'Livraison estimée',
    'standardDelivery': 'Livraison standard',
    'expressDelivery': 'Livraison express',
    'orderDetails': 'Détails de la commande',
    'shippingMethod': 'Méthode d\'expédition',
    'paymentMethod': 'Méthode de paiement',
    'price': 'Prix',
    'language': 'Langue',
    'category': 'Catégorie',
    'author': 'Auteur',
    'publisher': 'Éditeur',
    'quantity': 'Quantité',
    'total': 'Total',
    'checkout': 'Commander',
    'continueShopping': 'Continuer les Achats',
    'removeItem': 'Supprimer',
    'emptyCart': 'Votre panier est vide',
    'description': 'Description',
    'inStock': 'En Stock',
    'outOfStock': 'Rupture de Stock',
    'relatedItems': 'Articles Similaires',
    'sendMessage': 'Envoyer un Message',
    'yourName': 'Votre Nom',
    'yourEmail': 'Votre Email',
    'message': 'Message',
    'submit': 'Envoyer',
    'allBooks': 'Tous les Livres',
    'allQuran': 'Tous les Corans',
    'filterBy': 'Filtrer Par',
    'sortBy': 'Trier Par',
    'productDetails': 'Détails du Produit',
    'updateCart': 'Mettre à Jour le Panier',
    'back': 'Retour',
    'details': 'Détails',
    'shipping': 'Expédition',
    'share': 'Partager',
    'save': 'Sauvegarder',
    'viewAllBooks': 'Voir Tous les Livres',
    'viewAllQurans': 'Voir Tous les Corans',
    'bookNotFound': 'Livre Non Trouvé',
    'bookNotFoundDesc': 'Désolé, nous n\'avons pas pu trouver le livre que vous cherchez.',
    'returnToBooks': 'Retourner aux Livres',
    'quranNotFound': 'Coran Non Trouvé',
    'quranNotFoundDesc': 'Désolé, nous n\'avons pas pu trouver le Coran que vous cherchez.',
    'returnToQuran': 'Retourner aux Corans',
    'relatedQurans': 'Corans Similaires',
    'days': 'Jours',
    'deliveryTime': 'Délai de Livraison',
    'shippingInfo': 'Informations de Livraison',
    'shippingInfoDesc': 'Nous expédions tous les livres avec un emballage sécurisé pour garantir qu\'ils arrivent en parfait état.',
    'specialHandling': 'Manipulation Spéciale',
    'quranSpecialHandlingDesc': 'Le Coran est manipulé avec un soin et un respect particuliers lors de l\'emballage et de l\'expédition.',
    'reverentPackaging': 'Tous nos Corans sont emballés avec respect et expédiés dans un emballage protecteur.',
    'translator': 'Traducteur',
    'holyQuran': 'Saint Coran'
  },
  ar: {
    'home': 'الرئيسية',
    'books': 'الكتب',
    'quran': 'القرآن',
    'cart': 'السلة',
    'contact': 'اتصل بنا',
    'account': 'الحساب',
    'search': 'بحث',
    'featured': 'كتب مميزة',
    'newArrivals': 'وصل حديثاً',
    'bestSellers': 'الأكثر مبيعاً',
    'quranCollections': 'مجموعات القرآن',
    'islamicBooks': 'كتب إسلامية',
    'addToCart': 'أضف إلى السلة',
    'viewDetails': 'عرض التفاصيل',
    'contactUs': 'اتصل بنا',
    'about': 'من نحن',
    'address': 'مساكن، سوسة، تونس',
    'copyright': '© 2025 كتبكم. جميع الحقوق محفوظة.',
    'subscribeSuccess': 'تم الاشتراك بنجاح!',
    'subscribeMessage': 'شكراً لاشتراكك في نشرتنا الإخبارية.',
    'invalidEmail': 'يرجى إدخال عنوان بريد إلكتروني صحيح.',
    'shop': 'تسوق',
    'customerService': 'خدمة العملاء',
    'connectWithUs': 'تواصل معنا',
    'paymentMethods': 'طرق الدفع',
    'cashOnDelivery': 'الدفع عند الاستلام',
    'myCart': 'سلة التسوق',
    'shippingPolicy': 'سياسة الشحن',
    'faq': 'الأسئلة الشائعة',
    'privacyPolicy': 'سياسة الخصوصية',
    'termsOfService': 'شروط الخدمة',
    'sitemap': 'خريطة الموقع',
    'allRightsReserved': 'جميع الحقوق محفوظة',
    'businessDays': 'أيام عمل',
    'estimatedDelivery': 'موعد التسليم المتوقع',
    'standardDelivery': 'التوصيل القياسي',
    'expressDelivery': 'التوصيل السريع',
    'orderDetails': 'تفاصيل الطلب',
    'shippingMethod': 'طريقة الشحن',
    'paymentMethod': 'طريقة الدفع',
    'price': 'السعر',
    'language': 'اللغة',
    'category': 'التصنيف',
    'author': 'المؤلف',
    'publisher': 'الناشر',
    'quantity': 'الكمية',
    'total': 'المجموع',
    'checkout': 'إتمام الشراء',
    'continueShopping': 'مواصلة التسوق',
    'removeItem': 'إزالة',
    'emptyCart': 'سلة التسوق فارغة',
    'description': 'الوصف',
    'inStock': 'متوفر',
    'outOfStock': 'غير متوفر',
    'relatedItems': 'منتجات ذات صلة',
    'sendMessage': 'إرسال رسالة',
    'yourName': 'الإسم',
    'yourEmail': 'البريد الإلكتروني',
    'message': 'الرسالة',
    'submit': 'إرسال',
    'allBooks': 'جميع الكتب',
    'allQuran': 'جميع المصاحف',
    'filterBy': 'تصفية حسب',
    'sortBy': 'ترتيب حسب',
    'productDetails': 'تفاصيل المنتج',
    'updateCart': 'تحديث السلة',
    'back': 'رجوع',
    'details': 'التفاصيل',
    'shipping': 'الشحن',
    'share': 'مشاركة',
    'save': 'حفظ',
    'viewAllBooks': 'عرض كل الكتب',
    'viewAllQurans': 'عرض كل المصاحف',
    'bookNotFound': 'الكتاب غير موجود',
    'bookNotFoundDesc': 'نأسف، لم نتمكن من العثور على الكتاب الذي تبحث عنه.',
    'returnToBooks': 'العودة إلى الكتب',
    'quranNotFound': 'المصحف غير موجود',
    'quranNotFoundDesc': 'نأسف، لم نتمكن من العثور على المصحف الذي تبحث عنه.',
    'returnToQuran': 'العودة إلى المصاحف',
    'relatedQurans': 'مصاحف ذات صلة',
    'days': 'أيام',
    'deliveryTime': 'وقت التوصيل',
    'shippingInfo': 'معلومات الشحن',
    'shippingInfoDesc': 'نقوم بشحن جميع الكتب بتغليف آمن لضمان وصولها في حالة ممتازة.',
    'specialHandling': 'معاملة خاصة',
    'quranSpecialHandlingDesc': 'يتم التعامل مع القرآن بعناية واحترام خاصين أثناء التعبئة والشحن.',
    'reverentPackaging': 'يتم تغليف جميع مصاحفنا بعناية واحترام وشحنها في عبوات واقية.',
    'translator': 'المترجم',
    'holyQuran': 'القرآن الكريم'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>('en');

  const changeLanguage = (lang: Language) => {
    setLanguage(lang);
    document.documentElement.lang = lang;
  };

  const translate = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: changeLanguage, t: translate }}>
      {children}
    </LanguageContext.Provider>
  );
};
