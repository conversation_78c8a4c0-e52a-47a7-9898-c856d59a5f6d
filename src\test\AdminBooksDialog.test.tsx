import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AdminBooks from '../pages/admin/AdminBooks';

// Mock the dependencies
vi.mock('../components/AdminSidebar', () => ({
  default: () => <div data-testid="admin-sidebar">Admin Sidebar</div>
}));

vi.mock('../components/BackToDashboardButton', () => ({
  default: ({ className }: { className?: string }) => (
    <button className={className} data-testid="back-to-dashboard">
      Back to Dashboard
    </button>
  )
}));

vi.mock('../lib/supabase', () => ({
  getBooks: vi.fn().mockResolvedValue([]),
  getQuranBooks: vi.fn().mockResolvedValue([]),
  createBook: vi.fn().mockResolvedValue({ id: 1, title: 'Test Book' }),
  createQuranBook: vi.fn().mockResolvedValue({ id: 1, title: 'Test Quran Book' }),
  updateBook: vi.fn().mockResolvedValue({ id: 1, title: 'Updated Book' }),
  updateQuranBook: vi.fn().mockResolvedValue({ id: 1, title: 'Updated Quran Book' }),
  deleteBook: vi.fn().mockResolvedValue(true),
  deleteQuranBook: vi.fn().mockResolvedValue(true)
}));

vi.mock('../hooks/useToast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

describe('AdminBooks Dialog Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should open add dialog without errors', async () => {
    render(<AdminBooks />);
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Book Management')).toBeInTheDocument();
    });

    // Find and click the "Add New Book" button
    const addButton = screen.getByText('Add New Book');
    expect(addButton).toBeInTheDocument();

    // Click the button - this should not cause any portal errors
    fireEvent.click(addButton);

    // Wait for dialog to open
    await waitFor(() => {
      expect(screen.getByText('Add New Book')).toBeInTheDocument();
    });

    // Verify dialog content is present
    expect(screen.getByLabelText('Title')).toBeInTheDocument();
    expect(screen.getByLabelText('Author')).toBeInTheDocument();
    expect(screen.getByLabelText('Price')).toBeInTheDocument();
  });

  it('should close dialog properly without errors', async () => {
    render(<AdminBooks />);
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Book Management')).toBeInTheDocument();
    });

    // Open dialog
    const addButton = screen.getByText('Add New Book');
    fireEvent.click(addButton);

    // Wait for dialog to open
    await waitFor(() => {
      expect(screen.getByText('Add New Book')).toBeInTheDocument();
    });

    // Find and click cancel button
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    // Wait for dialog to close
    await waitFor(() => {
      expect(screen.queryByText('Add New Book')).not.toBeInTheDocument();
    });
  });

  it('should handle multiple dialog open/close cycles without errors', async () => {
    render(<AdminBooks />);
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Book Management')).toBeInTheDocument();
    });

    const addButton = screen.getByText('Add New Book');

    // Test multiple open/close cycles
    for (let i = 0; i < 3; i++) {
      // Open dialog
      fireEvent.click(addButton);

      // Wait for dialog to open
      await waitFor(() => {
        expect(screen.getByText('Add New Book')).toBeInTheDocument();
      });

      // Close dialog
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      // Wait for dialog to close
      await waitFor(() => {
        expect(screen.queryByText('Add New Book')).not.toBeInTheDocument();
      });
    }
  });
});
