import { supabase } from '@/integrations/supabase/client';

export interface ConnectionStatus {
  isConnected: boolean;
  error?: string;
  latency?: number;
  details?: {
    url: string;
    hasApiKey: boolean;
    timestamp: string;
  };
}

export const testSupabaseConnection = async (): Promise<ConnectionStatus> => {
  const startTime = Date.now();
  
  try {
    console.log('Testing Supabase connection...');
    
    // Test basic connection with a simple query
    const { data, error } = await supabase
      .from('books')
      .select('count')
      .limit(1);
    
    const latency = Date.now() - startTime;
    
    if (error) {
      console.error('Supabase connection test failed:', error);
      
      // Check for specific error types
      if (error.message?.includes('Failed to fetch') || 
          error.message?.includes('CORS') ||
          error.message?.includes('Network')) {
        return {
          isConnected: false,
          error: 'CORS/Network Error: Unable to connect to Supabase. This is likely due to CORS policy or network issues.',
          latency,
          details: {
            url: import.meta.env.VITE_SUPABASE_URL || 'Not configured',
            hasApiKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
            timestamp: new Date().toISOString()
          }
        };
      }
      
      return {
        isConnected: false,
        error: `Database Error: ${error.message}`,
        latency,
        details: {
          url: import.meta.env.VITE_SUPABASE_URL || 'Not configured',
          hasApiKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
          timestamp: new Date().toISOString()
        }
      };
    }
    
    console.log('Supabase connection test successful');
    return {
      isConnected: true,
      latency,
      details: {
        url: import.meta.env.VITE_SUPABASE_URL || 'Not configured',
        hasApiKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
        timestamp: new Date().toISOString()
      }
    };
    
  } catch (error) {
    const latency = Date.now() - startTime;
    console.error('Supabase connection test error:', error);
    
    return {
      isConnected: false,
      error: `Connection Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      latency,
      details: {
        url: import.meta.env.VITE_SUPABASE_URL || 'Not configured',
        hasApiKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
        timestamp: new Date().toISOString()
      }
    };
  }
};

export const logConnectionStatus = async (): Promise<void> => {
  const status = await testSupabaseConnection();
  
  console.group('🔗 Supabase Connection Status');
  console.log('Connected:', status.isConnected ? '✅' : '❌');
  console.log('Latency:', status.latency ? `${status.latency}ms` : 'N/A');
  
  if (status.error) {
    console.error('Error:', status.error);
  }
  
  if (status.details) {
    console.log('Details:', status.details);
  }
  
  console.groupEnd();
  
  // Show user-friendly message in development
  if (import.meta.env.DEV) {
    if (!status.isConnected) {
      console.warn(
        '⚠️ Supabase connection failed. The app will use fallback data for demonstration purposes.'
      );
    } else {
      console.info('✅ Supabase connection successful. Using live data.');
    }
  }
};
