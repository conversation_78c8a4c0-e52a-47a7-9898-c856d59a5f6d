
import { supabase } from '@/integrations/supabase/client';

export interface BookData {
  id: string;
  title: string;
  author: string;
  price: number;
  description: string;
  coverImage: string;
  category: string;
  language: string;
  publisher: string;
  rating: number;
  inStock: boolean;
  featured: boolean;
  newArrival: boolean;
  bestSeller: boolean;
  pages: number;
  publication_date: string;
  isbn: string;
  stock: number;
  reviews: any[];
}

// Fallback data for when Supabase is not available
const fallbackBooks: BookData[] = [
  {
    id: '1',
    title: 'The Richest Man in Babylon',
    author: '<PERSON>',
    price: 25.99,
    description: 'A classic book about financial wisdom and wealth building.',
    coverImage: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400',
    category: 'Finance',
    language: 'English',
    publisher: 'Signet',
    rating: 4.5,
    inStock: true,
    featured: true,
    newArrival: false,
    bestSeller: true,
    pages: 144,
    publication_date: '1926',
    isbn: '978-0451205360',
    stock: 10,
    reviews: []
  },
  {
    id: '2',
    title: 'Think and Grow Rich',
    author: '<PERSON> Hill',
    price: 22.99,
    description: 'The landmark bestseller about achieving success and wealth.',
    coverImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    category: 'Self-Help',
    language: 'English',
    publisher: 'The Ralston Society',
    rating: 4.7,
    inStock: true,
    featured: true,
    newArrival: true,
    bestSeller: false,
    pages: 238,
    publication_date: '1937',
    isbn: '978-1585424337',
    stock: 15,
    reviews: []
  },
  {
    id: '3',
    title: 'The 7 Habits of Highly Effective People',
    author: 'Stephen R. Covey',
    price: 28.99,
    description: 'A powerful lesson in personal change and effectiveness.',
    coverImage: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
    category: 'Self-Help',
    language: 'English',
    publisher: 'Free Press',
    rating: 4.6,
    inStock: true,
    featured: false,
    newArrival: false,
    bestSeller: true,
    pages: 381,
    publication_date: '1989',
    isbn: '978-1982137274',
    stock: 8,
    reviews: []
  }
];

const fallbackQuranBooks: BookData[] = [
  {
    id: 'q1',
    title: 'The Holy Quran - Arabic with English Translation',
    author: 'Various Translators',
    price: 35.99,
    description: 'Complete Quran with Arabic text and English translation.',
    coverImage: 'https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=400',
    category: 'Quran',
    language: 'Arabic/English',
    publisher: 'Islamic Foundation',
    rating: 5.0,
    inStock: true,
    featured: true,
    newArrival: false,
    bestSeller: true,
    pages: 604,
    publication_date: '2020',
    isbn: '978-0860372622',
    stock: 20,
    reviews: []
  },
  {
    id: 'q2',
    title: 'Quran Majeed - French Translation',
    author: 'Muhammad Hamidullah',
    price: 32.99,
    description: 'French translation of the Holy Quran with Arabic text.',
    coverImage: 'https://images.unsplash.com/photo-1585036156171-384164a8c675?w=400',
    category: 'Quran',
    language: 'Arabic/French',
    publisher: 'Dar Al-Kitab Al-Arabi',
    rating: 4.8,
    inStock: true,
    featured: true,
    newArrival: true,
    bestSeller: false,
    pages: 650,
    publication_date: '2019',
    isbn: '978-2745178947',
    stock: 12,
    reviews: []
  }
];

export const fetchBooks = async (): Promise<BookData[]> => {
  try {
    console.log('Attempting to fetch books from Supabase...');

    const { data: dbBooks, error } = await supabase
      .from('books')
      .select('*');

    if (error) {
      console.error("Error fetching books:", error);

      // Check if it's a CORS or network error
      if (error.message?.includes('Failed to fetch') ||
          error.message?.includes('CORS') ||
          error.message?.includes('Network')) {
        console.warn("Network/CORS error detected. Using fallback data...");
        return fallbackBooks;
      }

      throw error;
    }

    if (!dbBooks || dbBooks.length === 0) {
      console.warn("No books found in the database. Using fallback data...");
      return fallbackBooks;
    }

    const books: BookData[] = dbBooks.map(dbBook => {
      const book: BookData = {
        id: dbBook.id,
        title: dbBook.title || '',
        author: dbBook.author || '',
        price: dbBook.price || 0,
        description: dbBook.description || '',
        coverImage: dbBook.cover_image || '',
        category: dbBook.category || '',
        language: dbBook.language || '',
        publisher: dbBook.publisher || '',
        rating: dbBook.rating || 0,
        inStock: dbBook.in_stock || false,
        featured: dbBook.featured || false,
        newArrival: dbBook.new_arrival || false,
        bestSeller: dbBook.best_seller || false,
        pages: 0, // Default value since it might not be in the database
        publication_date: '', // Default value
        isbn: '', // Default value
        stock: dbBook.in_stock ? 1 : 0,
        reviews: []
      };

      return book;
    });

    console.log(`Successfully fetched ${books.length} books from Supabase`);
    return books;
  } catch (error) {
    console.error("Error processing book data:", error);
    console.warn("Falling back to local data due to error...");
    return fallbackBooks;
  }
};

export const fetchQuranBooks = async (): Promise<BookData[]> => {
  try {
    console.log('Attempting to fetch Quran books from Supabase...');

    const { data: dbBooks, error } = await supabase
      .from('quran_books')
      .select('*');

    if (error) {
      console.error("Error fetching quran books:", error);

      // Check if it's a CORS or network error
      if (error.message?.includes('Failed to fetch') ||
          error.message?.includes('CORS') ||
          error.message?.includes('Network')) {
        console.warn("Network/CORS error detected. Using fallback Quran data...");
        return fallbackQuranBooks;
      }

      throw error;
    }

    if (!dbBooks || dbBooks.length === 0) {
      console.warn("No quran books found in the database. Using fallback data...");
      return fallbackQuranBooks;
    }

    const books: BookData[] = dbBooks.map(dbBook => ({
      id: dbBook.id,
      title: dbBook.title || '',
      author: dbBook.author || '',
      price: dbBook.price || 0,
      description: dbBook.description || '',
      coverImage: dbBook.cover_image || '',
      category: "Quran",
      language: dbBook.language || '',
      publisher: dbBook.publisher || '',
      rating: dbBook.rating || 0,
      inStock: dbBook.in_stock || false,
      featured: dbBook.featured || false,
      newArrival: dbBook.new_arrival || false,
      bestSeller: dbBook.best_seller || false,
      pages: 0, // Default value
      publication_date: '', // Default value
      isbn: '', // Default value
      stock: dbBook.in_stock ? 1 : 0,
      reviews: []
    }));

    console.log(`Successfully fetched ${books.length} Quran books from Supabase`);
    return books;
  } catch (error) {
    console.error("Error processing quran book data:", error);
    console.warn("Falling back to local Quran data due to error...");
    return fallbackQuranBooks;
  }
};

export const createBook = async (bookData: Partial<BookData>): Promise<BookData | null> => {
  try {
    console.log('createBook called with data:', bookData);

    const { coverImage, publication_date, inStock, ...rest } = bookData;

    const bookToInsert = {
      title: rest.title || '',
      author: rest.author || '',
      price: rest.price || 0,
      description: rest.description || '',
      cover_image: coverImage || '',
      category: rest.category || 'Fiction',
      language: rest.language || 'English',
      publisher: rest.publisher || 'Unknown',
      rating: rest.rating || 5,
      in_stock: inStock !== undefined ? inStock : true,
      featured: rest.featured || false,
      new_arrival: rest.newArrival || false,
      best_seller: rest.bestSeller || false
    };

    console.log('Data to insert into books table:', bookToInsert);

    const { data, error } = await supabase
      .from('books')
      .insert(bookToInsert)
      .select();

    console.log('Supabase response - data:', data, 'error:', error);

    if (error) {
      console.error("Error creating book:", error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.warn("No data returned after creating book.");
      return null;
    }

    // Convert the returned data to BookData format
    const newBook: BookData = {
      id: data[0].id,
      title: data[0].title || '',
      author: data[0].author || '',
      price: data[0].price || 0,
      description: data[0].description || '',
      coverImage: data[0].cover_image || '',
      category: data[0].category || '',
      language: data[0].language || '',
      publisher: data[0].publisher || '',
      rating: data[0].rating || 0,
      inStock: data[0].in_stock || false,
      featured: data[0].featured || false,
      newArrival: data[0].new_arrival || false,
      bestSeller: data[0].best_seller || false,
      pages: 0, // Default value
      publication_date: '', // Default value
      isbn: '', // Default value
      stock: data[0].in_stock ? 1 : 0,
      reviews: []
    };

    return newBook;
  } catch (error) {
    console.error("Error in createBook:", error);
    return null;
  }
};

export const updateBook = async (id: string, bookData: Partial<BookData>): Promise<BookData | null> => {
  try {
    const { coverImage, publication_date, inStock, ...rest } = bookData;

    const bookToUpdate = {
      ...(rest.title !== undefined && { title: rest.title }),
      ...(rest.author !== undefined && { author: rest.author }),
      ...(rest.price !== undefined && { price: rest.price }),
      ...(rest.description !== undefined && { description: rest.description }),
      ...(coverImage !== undefined && { cover_image: coverImage }),
      ...(rest.category !== undefined && { category: rest.category }),
      ...(rest.language !== undefined && { language: rest.language }),
      ...(rest.publisher !== undefined && { publisher: rest.publisher }),
      ...(rest.rating !== undefined && { rating: rest.rating }),
      ...(inStock !== undefined && { in_stock: inStock }),
      ...(rest.featured !== undefined && { featured: rest.featured }),
      ...(rest.newArrival !== undefined && { new_arrival: rest.newArrival }),
      ...(rest.bestSeller !== undefined && { best_seller: rest.bestSeller }),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('books')
      .update(bookToUpdate)
      .eq('id', id)
      .select();

    if (error) {
      console.error("Error updating book:", error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.warn("No data returned after updating book.");
      return null;
    }

    // Convert the returned data to BookData format
    const updatedBook: BookData = {
      id: data[0].id,
      title: data[0].title || '',
      author: data[0].author || '',
      price: data[0].price || 0,
      description: data[0].description || '',
      coverImage: data[0].cover_image || '',
      category: data[0].category || '',
      language: data[0].language || '',
      publisher: data[0].publisher || '',
      rating: data[0].rating || 0,
      inStock: data[0].in_stock || false,
      featured: data[0].featured || false,
      newArrival: data[0].new_arrival || false,
      bestSeller: data[0].best_seller || false,
      pages: 0, // Default value
      publication_date: '', // Default value
      isbn: '', // Default value
      stock: data[0].in_stock ? 1 : 0,
      reviews: []
    };

    return updatedBook;
  } catch (error) {
    console.error("Error in updateBook:", error);
    return null;
  }
};

export const deleteBook = async (id: string): Promise<boolean> => {
  try {
    console.log('🗑️ deleteBook called with id:', id);

    const { error } = await supabase
      .from('books')
      .delete()
      .eq('id', id);

    console.log('Supabase delete response - error:', error);

    if (error) {
      console.error("Error deleting book:", error);
      throw error;
    }

    console.log('✅ Book deleted successfully from books table');
    return true;
  } catch (error) {
    console.error("Error in deleteBook:", error);
    return false;
  }
};

// Add functions for Quran books management
export const createQuranBook = async (bookData: Partial<BookData>): Promise<BookData | null> => {
  try {
    console.log('createQuranBook called with data:', bookData);

    const { coverImage, publication_date, inStock, ...rest } = bookData;

    const bookToInsert = {
      title: rest.title || '',
      author: rest.author || '',
      price: rest.price || 0,
      description: rest.description || '',
      cover_image: coverImage || '',
      language: rest.language || 'Arabic',
      publisher: rest.publisher || 'Unknown',
      rating: rest.rating || 5,
      in_stock: inStock !== undefined ? inStock : true,
      featured: rest.featured || false,
      new_arrival: rest.newArrival || false,
      best_seller: rest.bestSeller || false
    };

    console.log('Data to insert into quran_books table:', bookToInsert);

    const { data, error } = await supabase
      .from('quran_books')
      .insert(bookToInsert)
      .select();

    console.log('Supabase response - data:', data, 'error:', error);

    if (error) {
      console.error("Error creating Quran book:", error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.warn("No data returned after creating Quran book.");
      return null;
    }

    // Convert the returned data to BookData format
    const newBook: BookData = {
      id: data[0].id,
      title: data[0].title || '',
      author: data[0].author || '',
      price: data[0].price || 0,
      description: data[0].description || '',
      coverImage: data[0].cover_image || '',
      category: "Quran",
      language: data[0].language || '',
      publisher: data[0].publisher || '',
      rating: data[0].rating || 0,
      inStock: data[0].in_stock || false,
      featured: data[0].featured || false,
      newArrival: data[0].new_arrival || false,
      bestSeller: data[0].best_seller || false,
      pages: 0, // Default value
      publication_date: '', // Default value
      isbn: '', // Default value
      stock: data[0].in_stock ? 1 : 0,
      reviews: []
    };

    return newBook;
  } catch (error) {
    console.error("Error in createQuranBook:", error);
    return null;
  }
};

export const updateQuranBook = async (id: string, bookData: Partial<BookData>): Promise<BookData | null> => {
  try {
    const { coverImage, publication_date, inStock, ...rest } = bookData;

    const bookToUpdate = {
      ...(rest.title !== undefined && { title: rest.title }),
      ...(rest.author !== undefined && { author: rest.author }),
      ...(rest.price !== undefined && { price: rest.price }),
      ...(rest.description !== undefined && { description: rest.description }),
      ...(coverImage !== undefined && { cover_image: coverImage }),
      ...(rest.language !== undefined && { language: rest.language }),
      ...(rest.publisher !== undefined && { publisher: rest.publisher }),
      ...(rest.rating !== undefined && { rating: rest.rating }),
      ...(inStock !== undefined && { in_stock: inStock }),
      ...(rest.featured !== undefined && { featured: rest.featured }),
      ...(rest.newArrival !== undefined && { new_arrival: rest.newArrival }),
      ...(rest.bestSeller !== undefined && { best_seller: rest.bestSeller }),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('quran_books')
      .update(bookToUpdate)
      .eq('id', id)
      .select();

    if (error) {
      console.error("Error updating Quran book:", error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.warn("No data returned after updating Quran book.");
      return null;
    }

    // Convert the returned data to BookData format
    const updatedBook: BookData = {
      id: data[0].id,
      title: data[0].title || '',
      author: data[0].author || '',
      price: data[0].price || 0,
      description: data[0].description || '',
      coverImage: data[0].cover_image || '',
      category: "Quran",
      language: data[0].language || '',
      publisher: data[0].publisher || '',
      rating: data[0].rating || 0,
      inStock: data[0].in_stock || false,
      featured: data[0].featured || false,
      newArrival: data[0].new_arrival || false,
      bestSeller: data[0].best_seller || false,
      pages: 0, // Default value
      publication_date: '', // Default value
      isbn: '', // Default value
      stock: data[0].in_stock ? 1 : 0,
      reviews: []
    };

    return updatedBook;
  } catch (error) {
    console.error("Error in updateQuranBook:", error);
    return null;
  }
};

export const deleteQuranBook = async (id: string): Promise<boolean> => {
  try {
    console.log('🗑️ deleteQuranBook called with id:', id);

    const { error } = await supabase
      .from('quran_books')
      .delete()
      .eq('id', id);

    console.log('Supabase delete response - error:', error);

    if (error) {
      console.error("Error deleting Quran book:", error);
      throw error;
    }

    console.log('✅ Quran book deleted successfully from quran_books table');
    return true;
  } catch (error) {
    console.error("Error in deleteQuranBook:", error);
    return false;
  }
};

// Function to fetch books by category
export const fetchBooksByCategory = async (categoryId: string): Promise<BookData[]> => {
  try {
    console.log(`Fetching books for category: ${categoryId}`);

    // Special case for Quran category
    if (categoryId === 'quran') {
      return await fetchQuranBooks();
    }

    // For other categories, fetch from books table
    const { data: dbBooks, error } = await supabase
      .from('books')
      .select('*')
      .ilike('category', `%${categoryId}%`);

    if (error) {
      console.error(`Error fetching books for category ${categoryId}:`, error);
      throw error;
    }

    if (!dbBooks || dbBooks.length === 0) {
      console.warn(`No books found for category ${categoryId}.`);
      return [];
    }

    // Map database books to BookData format
    const books: BookData[] = dbBooks.map(dbBook => ({
      id: dbBook.id,
      title: dbBook.title || '',
      author: dbBook.author || '',
      price: dbBook.price || 0,
      description: dbBook.description || '',
      coverImage: dbBook.cover_image || '',
      category: dbBook.category || '',
      language: dbBook.language || '',
      publisher: dbBook.publisher || '',
      rating: dbBook.rating || 0,
      inStock: dbBook.in_stock || false,
      featured: dbBook.featured || false,
      newArrival: dbBook.new_arrival || false,
      bestSeller: dbBook.best_seller || false,
      pages: 0, // Default value
      publication_date: '', // Default value
      isbn: '', // Default value
      stock: dbBook.in_stock ? 1 : 0,
      reviews: []
    }));

    return books;
  } catch (error) {
    console.error(`Error processing book data for category ${categoryId}:`, error);
    return [];
  }
};
