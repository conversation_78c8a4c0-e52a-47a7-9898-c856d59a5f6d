import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Link } from 'react-router-dom';
import { BookOpen, BookMarked, Bookmark, Users, ArrowRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const FeaturedCategories = () => {
  const { t, language } = useLanguage();
  const { theme } = useTheme();
  const isRTL = language === 'ar';

  return (
    <section className={`pt-24 pb-16 ${theme === 'light' ? 'bg-white' : 'bg-black'}`}>
      <div className="container">
        {/* Modern section header with clean design */}
        <div className="text-center mb-16 relative">
          {/* Subtle accent element */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-[#6e191c] rounded-full"></div>

          <Badge className={`mb-4 px-4 py-1.5 text-sm uppercase tracking-wider ${theme === 'light'
            ? 'bg-[#6e191c]/10 text-[#6e191c] border border-[#6e191c]/20'
            : 'bg-[#6e191c]/20 text-white border border-[#6e191c]/30'}`}>
            {t('categories')}
          </Badge>

          <h2 className={`text-3xl md:text-4xl font-serif font-bold mb-6 ${
            theme === 'light'
              ? 'text-[#6e191c]'
              : 'text-white'
          }`}>
            {language === 'en' ? "Browse by Categories" : language === 'fr' ? "Parcourir par Catégories" : "تصفح حسب الفئات"}
          </h2>

          <p className={`mt-3 max-w-2xl mx-auto ${
            theme === 'light'
              ? 'text-black/80'
              : 'text-white/80'
          }`}>
            {language === 'en'
              ? "Explore our curated collection of Islamic books and Quran editions, bringing wisdom and knowledge to your life."
              : language === 'fr'
                ? "Explorez notre collection sélectionnée de livres islamiques et d'éditions du Coran, apportant sagesse et connaissance à votre vie."
                : "استكشف مجموعتنا المختارة من الكتب الإسلامية وإصدارات القرآن، التي تجلب الحكمة والمعرفة إلى حياتك."}
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 md:gap-8">
          {/* Islamic Studies - Modern card design */}
          <Link
            to="/categories/islamic-studies"
            className={`group relative overflow-hidden rounded-md
              ${theme === 'light'
                ? 'bg-white border border-[#6e191c]/10 shadow-md'
                : 'bg-[#111] border border-[#6e191c]/20 shadow-md'}
              p-6 flex flex-col items-center justify-center text-center h-48
              transition-all duration-300
              hover:shadow-lg hover:border-[#6e191c]/30
              hover:-translate-y-1`}
          >
            {/* Clean background */}
            <div className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 bg-gradient-to-br from-[#6e191c]/10 to-transparent`}></div>

            {/* Modern icon with clean styling */}
            <div className={`relative z-10 mb-4 p-3 rounded-full
              ${theme === 'light'
                ? 'bg-[#6e191c]/10 text-[#6e191c]'
                : 'bg-[#6e191c]/20 text-[#6e191c]'}
              group-hover:scale-110 transition-all duration-300`}
            >
              <BookOpen className="h-6 w-6" />
            </div>

            {/* Category name with clean typography */}
            <h3 className={`relative z-10 font-medium text-base
              ${theme === 'light' ? 'text-black' : 'text-white'}`}
            >
              {language === 'en' ? "Islamic Studies" : language === 'fr' ? "Études Islamiques" : "الدراسات الإسلامية"}
            </h3>

            {/* Subtle accent line */}
            <div className="w-8 h-0.5 bg-[#6e191c] my-2 transition-all duration-300 group-hover:w-12"></div>
          </Link>

          {/* Quran - Modern card design */}
          <Link
            to="/quran"
            className={`group relative overflow-hidden rounded-md
              ${theme === 'light'
                ? 'bg-white border border-[#6e191c]/10 shadow-md'
                : 'bg-[#111] border border-[#6e191c]/20 shadow-md'}
              p-6 flex flex-col items-center justify-center text-center h-48
              transition-all duration-300
              hover:shadow-lg hover:border-[#6e191c]/30
              hover:-translate-y-1`}
          >
            {/* Clean background */}
            <div className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 bg-gradient-to-br from-[#6e191c]/10 to-transparent`}></div>

            {/* Modern icon with clean styling */}
            <div className={`relative z-10 mb-4 p-3 rounded-full
              ${theme === 'light'
                ? 'bg-[#6e191c]/10 text-[#6e191c]'
                : 'bg-[#6e191c]/20 text-[#6e191c]'}
              group-hover:scale-110 transition-all duration-300`}
            >
              <BookMarked className="h-6 w-6" />
            </div>

            {/* Category name with clean typography */}
            <h3 className={`relative z-10 font-medium text-base
              ${theme === 'light' ? 'text-black' : 'text-white'}`}
            >
              {language === 'en' ? "Quran" : language === 'fr' ? "Coran" : "القرآن"}
            </h3>

            {/* Subtle accent line */}
            <div className="w-8 h-0.5 bg-[#6e191c] my-2 transition-all duration-300 group-hover:w-12"></div>
          </Link>

          {/* History - Modern card design */}
          <Link
            to="/categories/history"
            className={`group relative overflow-hidden rounded-md
              ${theme === 'light'
                ? 'bg-white border border-[#6e191c]/10 shadow-md'
                : 'bg-[#111] border border-[#6e191c]/20 shadow-md'}
              p-6 flex flex-col items-center justify-center text-center h-48
              transition-all duration-300
              hover:shadow-lg hover:border-[#6e191c]/30
              hover:-translate-y-1`}
          >
            {/* Clean background */}
            <div className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 bg-gradient-to-br from-[#6e191c]/10 to-transparent`}></div>

            {/* Modern icon with clean styling */}
            <div className={`relative z-10 mb-4 p-3 rounded-full
              ${theme === 'light'
                ? 'bg-[#6e191c]/10 text-[#6e191c]'
                : 'bg-[#6e191c]/20 text-[#6e191c]'}
              group-hover:scale-110 transition-all duration-300`}
            >
              <Bookmark className="h-6 w-6" />
            </div>

            {/* Category name with clean typography */}
            <h3 className={`relative z-10 font-medium text-base
              ${theme === 'light' ? 'text-black' : 'text-white'}`}
            >
              {language === 'en' ? "History" : language === 'fr' ? "Histoire" : "التاريخ"}
            </h3>

            {/* Subtle accent line */}
            <div className="w-8 h-0.5 bg-[#6e191c] my-2 transition-all duration-300 group-hover:w-12"></div>
          </Link>

          {/* Biography - Modern card design */}
          <Link
            to="/categories/biography"
            className={`group relative overflow-hidden rounded-md
              ${theme === 'light'
                ? 'bg-white border border-[#6e191c]/10 shadow-md'
                : 'bg-[#111] border border-[#6e191c]/20 shadow-md'}
              p-6 flex flex-col items-center justify-center text-center h-48
              transition-all duration-300
              hover:shadow-lg hover:border-[#6e191c]/30
              hover:-translate-y-1`}
          >
            {/* Clean background */}
            <div className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 bg-gradient-to-br from-[#6e191c]/10 to-transparent`}></div>

            {/* Modern icon with clean styling */}
            <div className={`relative z-10 mb-4 p-3 rounded-full
              ${theme === 'light'
                ? 'bg-[#6e191c]/10 text-[#6e191c]'
                : 'bg-[#6e191c]/20 text-[#6e191c]'}
              group-hover:scale-110 transition-all duration-300`}
            >
              <Users className="h-6 w-6" />
            </div>

            {/* Category name with clean typography */}
            <h3 className={`relative z-10 font-medium text-base
              ${theme === 'light' ? 'text-black' : 'text-white'}`}
            >
              {language === 'en' ? "Biography" : language === 'fr' ? "Biographie" : "السيرة الذاتية"}
            </h3>

            {/* Subtle accent line */}
            <div className="w-8 h-0.5 bg-[#6e191c] my-2 transition-all duration-300 group-hover:w-12"></div>
          </Link>

          {/* Children - Modern card design */}
          <Link
            to="/categories/children"
            className={`group relative overflow-hidden rounded-md
              ${theme === 'light'
                ? 'bg-white border border-[#6e191c]/10 shadow-md'
                : 'bg-[#111] border border-[#6e191c]/20 shadow-md'}
              p-6 flex flex-col items-center justify-center text-center h-48
              transition-all duration-300
              hover:shadow-lg hover:border-[#6e191c]/30
              hover:-translate-y-1`}
          >
            {/* Clean background */}
            <div className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 bg-gradient-to-br from-[#6e191c]/10 to-transparent`}></div>

            {/* Modern icon with clean styling */}
            <div className={`relative z-10 mb-4 p-3 rounded-full
              ${theme === 'light'
                ? 'bg-[#6e191c]/10 text-[#6e191c]'
                : 'bg-[#6e191c]/20 text-[#6e191c]'}
              group-hover:scale-110 transition-all duration-300`}
            >
              <BookOpen className="h-6 w-6" />
            </div>

            {/* Category name with clean typography */}
            <h3 className={`relative z-10 font-medium text-base
              ${theme === 'light' ? 'text-black' : 'text-white'}`}
            >
              {language === 'en' ? "Children" : language === 'fr' ? "Enfants" : "الأطفال"}
            </h3>

            {/* Subtle accent line */}
            <div className="w-8 h-0.5 bg-[#6e191c] my-2 transition-all duration-300 group-hover:w-12"></div>
          </Link>

          {/* All Categories - Modern card design */}
          <Link
            to="/categories"
            className={`group relative overflow-hidden rounded-md
              bg-[#6e191c] border border-[#6e191c]/80 shadow-md
              p-6 flex flex-col items-center justify-center text-center h-48
              transition-all duration-300
              hover:shadow-lg hover:bg-[#8a1e24]
              hover:-translate-y-1`}
          >
            {/* Modern icon with clean styling */}
            <div className="relative z-10 mb-4 p-3 rounded-full
              bg-white/20 text-white
              group-hover:scale-110 transition-all duration-300 group-hover:bg-white/30">
              <ArrowRight className="h-6 w-6 group-hover:translate-x-1 transition-transform duration-300" />
            </div>

            {/* Category name with clean typography */}
            <h3 className="relative z-10 font-medium text-base text-white">
              {language === 'en' ? "All Categories" : language === 'fr' ? "Toutes les Catégories" : "جميع الفئات"}
            </h3>

            {/* Subtle accent line */}
            <div className="w-8 h-0.5 bg-white my-2 transition-all duration-300 group-hover:w-12"></div>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCategories;
