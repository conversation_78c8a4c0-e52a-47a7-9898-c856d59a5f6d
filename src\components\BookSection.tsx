import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { BookData } from '@/data/books';
import BookCard from '@/components/BookCard';
import { Button } from '@/components/ui/button';
import { ChevronRight, BookOpen } from 'lucide-react';
import { Link } from 'react-router-dom';

interface BookSectionProps {
  title: string;
  books: BookData[];
  linkTo: string;
  icon: React.ReactNode;
  isLoading: boolean;
  ref?: React.RefObject<HTMLDivElement>;
}

const BookSection: React.FC<BookSectionProps> = React.forwardRef<HTMLDivElement, BookSectionProps>(
  ({ title, books, linkTo, icon, isLoading }, ref) => {
    const { t, language } = useLanguage();
    const { theme } = useTheme();
    const isRTL = language === 'ar';

    return (
      <section
        ref={ref}
        className={`py-24 relative reveal ${
          theme === 'light'
            ? 'bg-white'
            : 'bg-gradient-to-b from-[#050505] via-[#0a0a0a] to-[#050505]'
        }`}
      >
        {/* 🌙 Premium Decorative Background Elements for Dark Mode */}
        {theme === 'dark' && (
          <>
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
              {/* 💫 Ultra Premium Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-[#050505] via-[#0a0a0a] to-[#050505] z-0"></div>

              {/* 🌟 Majestic Burgundy Glow Effects */}
              <div className="absolute -top-20 right-0 w-[50rem] h-[50rem] rounded-full bg-gradient-radial from-[#6e191c]/15 via-[#6e191c]/8 to-transparent blur-3xl opacity-70 z-0 animate-pulse-slow"></div>

              <div className="absolute -bottom-40 -left-20 w-[40rem] h-[40rem] rounded-full bg-gradient-radial from-[#8a1e24]/12 via-[#6e191c]/6 to-transparent blur-3xl opacity-60 z-0 animate-floating"></div>

              {/* Subtle pattern overlay */}
              <div className="absolute inset-0 bg-[#1a1a1a] opacity-10 z-0"></div>
            </div>
          </>
        )}

        <div className="container relative z-10">
          {/* Enhanced section header with decorative elements */}
          <div className={`flex justify-between items-center mb-16 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex items-center">
              {/* Enhanced icon container with better styling */}
              <div className={`${
                theme === 'light'
                  ? 'bg-[#6e191c]/5 text-[#6e191c]'
                  : 'bg-[#6e191c]/10 text-[#6e191c]'
                } p-4 rounded-full mr-5 shadow-sm`}
              >
                {icon}
              </div>

              {/* Enhanced section title with decorative underline */}
              <div className="relative">
                <h2 className={`text-2xl md:text-3xl font-serif font-bold ${
                  theme === 'light'
                    ? 'text-[#6e191c]'
                    : 'text-white'
                }`}>{t(title)}</h2>
                <div className={`h-1 w-1/2 mt-2 rounded-full ${
                  theme === 'light'
                    ? 'bg-[#6e191c]/20'
                    : 'bg-[#6e191c]/30'
                }`}></div>
              </div>
            </div>

            {/* Enhanced view all button */}
            <Link to={linkTo}>
              <Button
                variant="ghost"
                className={`group px-5 py-2.5 rounded-lg transition-all duration-300 ${
                  theme === 'light'
                    ? 'hover:bg-[#6e191c]/5 text-[#6e191c]'
                    : 'hover:bg-[#6e191c]/10 text-white hover:text-white'
                }`}
              >
                <span className="mr-2">{t('viewAll')}</span>
                <ChevronRight className={`h-4 w-4 transition-transform group-hover:translate-x-1 ${isRTL ? 'rotate-180' : ''}`} />
              </Button>
            </Link>
          </div>

          {/* Enhanced book grid with better spacing and animations */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {isLoading ? (
              // Enhanced loading state with more attractive skeleton
              Array(4).fill(0).map((_, index) => (
                <div
                  key={index}
                  className="animate-pulse rounded-xl overflow-hidden shadow-md"
                  style={{
                    animationDelay: `${index * 150}ms`,
                    opacity: 1 - (index * 0.15)
                  }}
                >
                  {/* Book cover skeleton */}
                  <div className={`${
                    theme === 'light'
                      ? 'bg-[#6e191c]/5'
                      : 'bg-[#6e191c]/10'
                    } rounded-t-xl h-72 mb-4 relative overflow-hidden`}
                  >
                    {/* Animated loading gradient */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
                  </div>

                  {/* Content skeleton */}
                  <div className="px-4 pb-5">
                    {/* Title skeleton */}
                    <div className={`h-5 ${
                      theme === 'light'
                        ? 'bg-[#6e191c]/10'
                        : 'bg-[#6e191c]/15'
                      } rounded-full w-3/4 mb-3`}
                    ></div>

                    {/* Author skeleton */}
                    <div className={`h-4 ${
                      theme === 'light'
                        ? 'bg-[#6e191c]/5'
                        : 'bg-[#6e191c]/10'
                      } rounded-full w-1/2 mb-4`}
                    ></div>

                    {/* Price skeleton */}
                    <div className={`h-5 ${
                      theme === 'light'
                        ? 'bg-[#6e191c]/10'
                        : 'bg-[#6e191c]/15'
                      } rounded-full w-1/4`}
                    ></div>
                  </div>
                </div>
              ))
            ) : books.length > 0 ? (
              // Enhanced book cards with staggered animation
              books.map((book, index) => book && (
                <div
                  key={book.id}
                  className="opacity-0 animate-fade-in-up"
                  style={{ animationDelay: `${index * 150}ms`, animationFillMode: 'forwards' }}
                >
                  <BookCard book={book} />
                </div>
              ))
            ) : (
              // Enhanced empty state
              <div className="col-span-full text-center py-16">
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 ${
                  theme === 'light'
                    ? 'bg-[#6e191c]/5'
                    : 'bg-[#6e191c]/10'
                }`}>
                  <BookOpen className={`h-10 w-10 ${
                    theme === 'light'
                      ? 'text-[#6e191c]/60'
                      : 'text-[#6e191c]/70'
                  }`} />
                </div>
                <p className={`text-xl font-medium mb-3 ${
                  theme === 'light'
                    ? 'text-[#6e191c]'
                    : 'text-white'
                }`}>
                  {language === 'en' ? "No books available" :
                   language === 'fr' ? "Aucun livre disponible" :
                   "لا توجد كتب متاحة"}
                </p>
                <p className={`text-lg max-w-md mx-auto ${
                  theme === 'light'
                    ? 'text-black/70'
                    : 'text-white/70'
                }`}>
                  {language === 'en' ? "We couldn't find any books in this category. Please check back later." :
                   language === 'fr' ? "Nous n'avons trouvé aucun livre dans cette catégorie. Veuillez vérifier plus tard." :
                   "لم نتمكن من العثور على أي كتب في هذه الفئة. يرجى التحقق مرة أخرى لاحقًا."}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Section separator */}
        <div className="section-separator relative mt-16 overflow-hidden">
          <div className={`h-px w-full ${
            theme === 'light'
              ? 'bg-gradient-to-r from-transparent via-[#6e191c]/20 to-transparent'
              : 'bg-gradient-to-r from-transparent via-[#6e191c]/20 to-transparent'
          }`}></div>
        </div>
      </section>
    );
  }
);

BookSection.displayName = 'BookSection';

export default BookSection;
