
import React, { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Link } from 'react-router-dom';
import {
  Phone,
  Mail,
  MapPin,
  Facebook,
  Instagram,
  Twitter,
  Heart,
  Send,
  Youtube,
  Linkedin,
  CreditCard,
  ArrowRight,
  BookOpen,
  ShoppingBag,
  Truck,
  HelpCircle
} from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';

const Footer = () => {
  const { t, language } = useLanguage();
  const { theme } = useTheme();
  const isRTL = language === 'ar';
  const [email, setEmail] = useState('');

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !email.includes('@')) {
      toast.error(t('invalidEmail'));
      return;
    }

    toast.success(t('subscribeSuccess'), {
      description: t('subscribeMessage')
    });
    setEmail('');
  };

  return (
    <footer className={`${theme === 'light' ? 'bg-white' : 'bg-gradient-to-b from-[#050505] via-[#0a0a0a] to-[#050505]'} border-t ${theme === 'light' ? 'border-[#6e191c]/20' : 'border-[#6e191c]/50'}`}>
      {/* 🌙 Premium Newsletter Section */}
      <div className={`py-16 ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-gradient-to-br from-[#050505] via-[#0a0a0a] to-[#050505]'} relative overflow-hidden`}>
        {/* 💫 Ultra Premium Background for Dark Mode */}
        {theme === 'dark' && (
          <>
            <div className="absolute inset-0 bg-gradient-to-br from-[#050505] via-[#0a0a0a] to-[#050505]"></div>
            <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-[#6e191c]/70 to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-[#6e191c]/70 to-transparent"></div>

            {/* 🌟 Ambient Glow Effects */}
            <div className="absolute top-0 right-1/4 w-[400px] h-[200px] bg-gradient-radial from-[#6e191c]/20 via-[#6e191c]/10 to-transparent rounded-full blur-3xl opacity-60"></div>
            <div className="absolute bottom-0 left-1/4 w-[300px] h-[150px] bg-gradient-radial from-[#8a1e24]/15 via-[#6e191c]/8 to-transparent rounded-full blur-3xl opacity-50"></div>
          </>
        )}
        <div className="container relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div className={`max-w-md ${isRTL ? 'md:text-right' : ''}`}>
              <h3 className={`text-2xl font-serif font-bold mb-2 ${theme === 'light' ? 'text-white' : 'text-[#ded7ba]'}`}>
                {language === 'en' ? 'Subscribe to our newsletter' :
                 language === 'fr' ? 'Abonnez-vous à notre newsletter' :
                 'اشترك في نشرتنا الإخبارية'}
              </h3>
              <p className={`${theme === 'light' ? 'text-white/90' : 'text-[#ded7ba]/80'}`}>
                {language === 'en' ? 'Stay updated with our latest releases and promotions' :
                 language === 'fr' ? 'Restez informé de nos dernières sorties et promotions' :
                 'ابق على اطلاع بأحدث إصداراتنا وعروضنا الترويجية'}
              </p>
            </div>

            <form onSubmit={handleSubscribe} className="w-full max-w-md flex gap-2">
              <div className="relative flex-grow">
                <Input
                  type="email"
                  placeholder={language === 'en' ? 'Your email address' :
                               language === 'fr' ? 'Votre adresse e-mail' :
                               'عنوان بريدك الإلكتروني'}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`pr-10 ${theme === 'light'
                    ? 'bg-white border-white/30 text-[#6e191c] placeholder:text-[#6e191c]/60'
                    : 'bg-[#0d0d0d] border-[#6e191c]/40 text-[#ded7ba] placeholder:text-[#ded7ba]/60'}`}
                  required
                />
                <Mail className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${theme === 'light' ? 'text-[#6e191c]/60' : 'text-[#ded7ba]/60'}`} />
              </div>
              <Button
                type="submit"
                className={theme === 'light'
                  ? 'bg-white hover:bg-white/90 text-[#6e191c] border border-white/30 hover:border-white'
                  : 'bg-[#6e191c] hover:bg-[#8a1e24] text-[#ded7ba] border-[#6e191c]/40'}
              >
                {language === 'en' ? 'Subscribe' :
                 language === 'fr' ? 'S\'abonner' :
                 'اشترك'}
              </Button>
            </form>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className={`py-12 ${theme === 'light' ? 'bg-white' : 'bg-[#080808]'} relative`}>
        {/* Enhanced background for dark mode */}
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-b from-[#080808] to-[#0d0d0d] opacity-90"></div>
        )}
        <div className="container relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {/* Company Info */}
            <div className={isRTL ? 'text-right' : ''}>
              <Link to="/" className="inline-block mb-6 group">
                <span className="font-serif text-2xl font-bold transition-all duration-300">
                  <span className={theme === 'light' ? 'text-[#6e191c] group-hover:text-[#8a1e24]' : 'text-[#ded7ba] group-hover:text-[#6e191c]'}>Kotob</span>
                  <span className={theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}>com</span>
                </span>
              </Link>
              <p className={`mb-6 leading-relaxed ${theme === 'light' ? 'text-[#6e191c]/80' : 'text-[#ded7ba]/80'}`}>
                {language === 'en' ? 'Your trusted source for Islamic books and Quran editions in Tunisia.' :
                 language === 'fr' ? 'Votre source de confiance pour les livres islamiques et les éditions du Coran en Tunisie.' :
                 'مصدرك الموثوق للكتب الإسلامية وإصدارات القرآن في تونس.'}
              </p>
              <div className="space-y-3">
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} group`}>
                  <Phone className={`h-4 w-4 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} ${isRTL ? 'ml-3' : 'mr-3'} transition-transform duration-300 group-hover:scale-110`} />
                  <span className={`${theme === 'light' ? 'text-[#6e191c]' : 'text-[#ded7ba]'} transition-colors duration-300 group-hover:${theme === 'light' ? 'text-[#8a1e24]' : 'text-[#6e191c]'}`}>+216 73 123 456</span>
                </div>
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} group`}>
                  <Mail className={`h-4 w-4 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} ${isRTL ? 'ml-3' : 'mr-3'} transition-transform duration-300 group-hover:scale-110`} />
                  <span className={`${theme === 'light' ? 'text-[#6e191c]' : 'text-[#ded7ba]'} transition-colors duration-300 group-hover:${theme === 'light' ? 'text-[#8a1e24]' : 'text-[#6e191c]'}`}><EMAIL></span>
                </div>
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} group`}>
                  <MapPin className={`h-4 w-4 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} ${isRTL ? 'ml-3' : 'mr-3'} transition-transform duration-300 group-hover:scale-110`} />
                  <span className={`${theme === 'light' ? 'text-[#6e191c]' : 'text-[#ded7ba]'} transition-colors duration-300 group-hover:${theme === 'light' ? 'text-[#8a1e24]' : 'text-[#6e191c]'}`}>Msaken, Sousse, Tunisia</span>
                </div>
              </div>
            </div>

            {/* Shop */}
            <div className={isRTL ? 'text-right' : ''}>
              <h3 className={`text-lg font-serif font-bold mb-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}`}>
                {language === 'en' ? 'Shop' :
                 language === 'fr' ? 'Boutique' :
                 'تسوق'}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    to="/books"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <BookOpen className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'All Books' :
                     language === 'fr' ? 'Tous les livres' :
                     'جميع الكتب'}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/quran"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <BookOpen className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'Quran Editions' :
                     language === 'fr' ? 'Éditions du Coran' :
                     'إصدارات القرآن'}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/categories"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <BookOpen className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'Categories' :
                     language === 'fr' ? 'Catégories' :
                     'التصنيفات'}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/bestsellers"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <BookOpen className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'Best Sellers' :
                     language === 'fr' ? 'Meilleures ventes' :
                     'الأكثر مبيعًا'}
                  </Link>
                </li>
              </ul>
            </div>

            {/* Customer Service */}
            <div className={isRTL ? 'text-right' : ''}>
              <h3 className={`text-lg font-serif font-bold mb-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}`}>
                {language === 'en' ? 'Customer Service' :
                 language === 'fr' ? 'Service Client' :
                 'خدمة العملاء'}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    to="/cart"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <ShoppingBag className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'My Cart' :
                     language === 'fr' ? 'Mon panier' :
                     'سلة التسوق'}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/shipping"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <Truck className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'Shipping Policy' :
                     language === 'fr' ? 'Politique d\'expédition' :
                     'سياسة الشحن'}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <Mail className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'Contact Us' :
                     language === 'fr' ? 'Contactez-nous' :
                     'اتصل بنا'}
                  </Link>
                </li>
                <li>
                  <Link
                    to="/faq"
                    className={`flex items-center group ${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba] hover:text-[#6e191c]'} transition-all duration-300`}
                  >
                    <HelpCircle className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} ${theme === 'light' ? 'text-[#6e191c]/60 group-hover:text-[#6e191c]' : 'text-[#ded7ba]/60 group-hover:text-[#6e191c]'} transition-all duration-300 group-hover:scale-110`} />
                    {language === 'en' ? 'FAQ' :
                     language === 'fr' ? 'FAQ' :
                     'الأسئلة الشائعة'}
                  </Link>
                </li>
              </ul>
            </div>

            {/* Connect With Us */}
            <div className={isRTL ? 'text-right' : ''}>
              <h3 className={`text-lg font-serif font-bold mb-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}`}>
                {language === 'en' ? 'Connect With Us' :
                 language === 'fr' ? 'Connectez-vous avec nous' :
                 'تواصل معنا'}
              </h3>
              <div className="grid grid-cols-3 gap-3 mb-6">
                <a
                  href="https://facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center justify-center p-3 rounded-lg group ${theme === 'light'
                    ? 'bg-white hover:bg-[#6e191c] text-[#6e191c] hover:text-white border border-[#6e191c]/20 hover:border-[#6e191c]'
                    : 'bg-[#0d0d0d] hover:bg-[#121212] text-[#ded7ba] hover:text-[#6e191c] border border-[#6e191c]/20 hover:border-[#6e191c]/40'}
                    transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                  aria-label="Facebook"
                >
                  <Facebook className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center justify-center p-3 rounded-lg group ${theme === 'light'
                    ? 'bg-white hover:bg-[#6e191c] text-[#6e191c] hover:text-white border border-[#6e191c]/20 hover:border-[#6e191c]'
                    : 'bg-[#0d0d0d] hover:bg-[#121212] text-[#ded7ba] hover:text-[#6e191c] border border-[#6e191c]/20 hover:border-[#6e191c]/40'}
                    transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                  aria-label="Instagram"
                >
                  <Instagram className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center justify-center p-3 rounded-lg group ${theme === 'light'
                    ? 'bg-white hover:bg-[#6e191c] text-[#6e191c] hover:text-white border border-[#6e191c]/20 hover:border-[#6e191c]'
                    : 'bg-[#0d0d0d] hover:bg-[#121212] text-[#ded7ba] hover:text-[#6e191c] border border-[#6e191c]/20 hover:border-[#6e191c]/40'}
                    transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                  aria-label="Twitter"
                >
                  <Twitter className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
                </a>
                <a
                  href="https://youtube.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center justify-center p-3 rounded-lg group ${theme === 'light'
                    ? 'bg-white hover:bg-[#6e191c] text-[#6e191c] hover:text-white border border-[#6e191c]/20 hover:border-[#6e191c]'
                    : 'bg-[#0d0d0d] hover:bg-[#121212] text-[#ded7ba] hover:text-[#6e191c] border border-[#6e191c]/20 hover:border-[#6e191c]/40'}
                    transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                  aria-label="YouTube"
                >
                  <Youtube className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
                </a>
                <a
                  href="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex items-center justify-center p-3 rounded-lg group ${theme === 'light'
                    ? 'bg-white hover:bg-[#6e191c] text-[#6e191c] hover:text-white border border-[#6e191c]/20 hover:border-[#6e191c]'
                    : 'bg-[#0d0d0d] hover:bg-[#121212] text-[#ded7ba] hover:text-[#6e191c] border border-[#6e191c]/20 hover:border-[#6e191c]/40'}
                    transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                  aria-label="LinkedIn"
                >
                  <Linkedin className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
                </a>
              </div>

              <h4 className={`text-sm font-medium mb-3 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#ded7ba]'}`}>
                {language === 'en' ? 'Payment Methods' :
                 language === 'fr' ? 'Méthodes de paiement' :
                 'طرق الدفع'}
              </h4>
              <div className={`p-4 rounded-lg ${theme === 'light'
                ? 'bg-white border border-[#6e191c]/20 hover:border-[#6e191c]/40'
                : 'bg-[#0d0d0d] border border-[#6e191c]/20'}
                transition-all duration-300 hover:${theme === 'light' ? 'bg-[#6e191c]/5' : 'bg-[#121212] border-[#6e191c]/40'}`}>
                <div className="flex items-center justify-between">
                  <CreditCard className={`h-6 w-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} transition-transform duration-300 hover:scale-110`} />
                  <span className={`text-xs font-medium ${theme === 'light' ? 'text-[#6e191c]/80' : 'text-[#ded7ba]/80'}`}>
                    {language === 'en' ? 'Cash on Delivery' :
                     language === 'fr' ? 'Paiement à la livraison' :
                     'الدفع عند الاستلام'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <Separator className={`${theme === 'light' ? 'bg-[#6e191c]/20' : 'bg-[#6e191c]/40'} my-8`} />

          {/* Copyright and Bottom Links */}
          <div className="pt-4 flex flex-col md:flex-row justify-between items-center">
            <div className={`text-sm ${theme === 'light' ? 'text-[#6e191c]/80' : 'text-[#ded7ba]/80'} flex items-center mb-4 md:mb-0`}>
              © 2025 Kotobcom. {language === 'en' ? 'All rights reserved' :
                               language === 'fr' ? 'Tous droits réservés' :
                               'جميع الحقوق محفوظة'}.
              <Heart className={`h-4 w-4 mx-2 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} animate-pulse transition-colors duration-300`} />
            </div>

            <div className="flex flex-wrap gap-6 text-sm">
              <Link to="/privacy" className={`${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/80 hover:text-[#6e191c]'} transition-all duration-300 hover:scale-105`}>
                {language === 'en' ? 'Privacy Policy' :
                 language === 'fr' ? 'Politique de confidentialité' :
                 'سياسة الخصوصية'}
              </Link>
              <Link to="/terms" className={`${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/80 hover:text-[#6e191c]'} transition-all duration-300 hover:scale-105`}>
                {language === 'en' ? 'Terms of Service' :
                 language === 'fr' ? 'Conditions d\'utilisation' :
                 'شروط الخدمة'}
              </Link>
              <Link to="/sitemap" className={`${theme === 'light' ? 'text-[#6e191c]/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/80 hover:text-[#6e191c]'} transition-all duration-300 hover:scale-105`}>
                {language === 'en' ? 'Sitemap' :
                 language === 'fr' ? 'Plan du site' :
                 'خريطة الموقع'}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
