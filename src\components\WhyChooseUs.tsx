import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Card, CardContent } from '@/components/ui/card';
import { BookOpen, TruckIcon, BadgeCheck, HeartHandshake } from 'lucide-react';

const WhyChooseUs = () => {
  const { language } = useLanguage();
  const { theme } = useTheme();

  const features = [
    {
      icon: <BookOpen className="h-10 w-10" />,
      title: {
        en: "Curated Selection",
        fr: "Sélection Soignée",
        ar: "اختيار منسق"
      },
      description: {
        en: "Handpicked books and Quran editions chosen for quality and authenticity.",
        fr: "Livres et éditions du Coran sélectionnés à la main pour leur qualité et leur authenticité.",
        ar: "كتب وإصدارات قرآن مختارة يدويًا للجودة والأصالة."
      }
    },
    {
      icon: <TruckIcon className="h-10 w-10" />,
      title: {
        en: "Fast Delivery",
        fr: "Livraison Rapide",
        ar: "توصيل سريع"
      },
      description: {
        en: "Quick and reliable shipping to your doorstep across Tunisia.",
        fr: "Expédition rapide et fiable à votre porte partout en Tunisie.",
        ar: "شحن سريع وموثوق به إلى باب منزلك في جميع أنحاء تونس."
      }
    },
    {
      icon: <BadgeCheck className="h-10 w-10" />,
      title: {
        en: "Quality Guaranteed",
        fr: "Qualité Garantie",
        ar: "جودة مضمونة"
      },
      description: {
        en: "We ensure all our products meet the highest standards of quality.",
        fr: "Nous veillons à ce que tous nos produits répondent aux normes de qualité les plus élevées.",
        ar: "نحن نضمن أن جميع منتجاتنا تلبي أعلى معايير الجودة."
      }
    },
    {
      icon: <HeartHandshake className="h-10 w-10" />,
      title: {
        en: "Customer Support",
        fr: "Service Client",
        ar: "دعم العملاء"
      },
      description: {
        en: "Dedicated support team ready to assist you with any questions.",
        fr: "Équipe de support dédiée prête à vous aider pour toute question.",
        ar: "فريق دعم مخصص جاهز لمساعدتك في أي أسئلة."
      }
    }
  ];

  return (
    <section className={`py-16 ${theme === 'light' ? 'bg-[#f3edda]/30' : 'bg-background'}`}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={`text-3xl md:text-4xl font-serif font-bold mb-4 ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
            {language === 'en' ? "Why Choose Us" : 
             language === 'fr' ? "Pourquoi Nous Choisir" : 
             "لماذا تختارنا"}
          </h2>
          <p className={`max-w-2xl mx-auto ${theme === 'light' ? 'text-[#6b6a5c]' : 'text-muted-foreground'}`}>
            {language === 'en' ? "Discover the benefits of shopping with Msaken Bookstore Hub" : 
             language === 'fr' ? "Découvrez les avantages de faire vos achats avec Msaken Bookstore Hub" : 
             "اكتشف مزايا التسوق مع مركز مكتبة مساكن"}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card
              key={index}
              className={`border-none ${theme === 'light' ? 'bg-[#ece7d5]/70 hover:bg-[#ece7d5]' : 'bg-gradient-to-br from-[#0a0a0a]/80 via-[#0f0f0f]/70 to-[#0a0a0a]/80 hover:bg-gradient-to-br hover:from-[#0f0f0f]/90 hover:via-[#141414]/80 hover:to-[#0f0f0f]/90 border border-[#6e191c]/20 shadow-[0_8px_30px_rgba(0,0,0,0.6),0_4px_16px_rgba(110,25,28,0.2)]'} transition-all duration-500 hover:shadow-2xl hover:scale-105 group backdrop-blur-sm`}
            >
              <CardContent className="p-8 text-center relative">
                {/* 💫 Premium Glow Effect for Dark Mode */}
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-[#6e191c]/10 via-transparent to-[#8a1e24]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg"></div>
                )}
                <div className={`mx-auto w-20 h-20 flex items-center justify-center rounded-full mb-6 relative z-10 ${theme === 'light' ? 'bg-[#ded7ba]/50 text-[#5c5a4e]' : 'bg-gradient-to-br from-[#6e191c]/25 to-[#8a1e24]/20 text-[#f0ebe0] shadow-[0_4px_16px_rgba(110,25,28,0.4)]'} group-hover:scale-125 transition-all duration-500`}>
                  {feature.icon}
                </div>
                <h3 className={`text-xl font-bold mb-2 ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                  {feature.title[language as keyof typeof feature.title]}
                </h3>
                <p className={`${theme === 'light' ? 'text-[#6b6a5c]' : 'text-muted-foreground'}`}>
                  {feature.description[language as keyof typeof feature.description]}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
