
import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('dark');

  useEffect(() => {
    // Check if user has already selected a theme
    const hasSelectedTheme = localStorage.getItem('hasSelectedTheme');

    // If user has selected a theme before, use that theme
    if (hasSelectedTheme) {
      const savedTheme = localStorage.getItem('theme') as Theme;
      // Default to dark mode if no saved theme
      const initialTheme = savedTheme || 'dark';
      setTheme(initialTheme);
      applyTheme(initialTheme);
    } else {
      // For first-time visitors, we'll let the modal handle theme selection
      // But we'll still set dark mode as the default
      const initialTheme = 'dark';
      setTheme(initialTheme);
      applyTheme(initialTheme);
    }

    // Add listener for system preference changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      // Only auto-switch if user hasn't explicitly chosen a theme
      // Always prefer dark mode, but allow system to switch to light if preferred
      if (!localStorage.getItem('hasSelectedTheme')) {
        // If system prefers light, we'll respect that, otherwise stay dark
        const newTheme = e.matches ? 'dark' : 'light';
        setTheme(newTheme);
        applyTheme(newTheme);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const applyTheme = (newTheme: Theme) => {
    const root = window.document.documentElement;

    // Add smooth transition class
    root.classList.add('transition-colors', 'duration-300');

    // Apply theme
    if (newTheme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Save theme preference
    localStorage.setItem('theme', newTheme);
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    applyTheme(newTheme);
    // Mark that user has explicitly selected a theme
    localStorage.setItem('hasSelectedTheme', 'true');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
