import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Sun, Moon } from 'lucide-react';

const ThemeSelectorModal = () => {
  const { setTheme } = useTheme();
  const { t, language } = useLanguage();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    // Check if this is the first visit
    const hasSelectedTheme = localStorage.getItem('hasSelectedTheme');

    if (!hasSelectedTheme) {
      // Show the modal on first visit
      setOpen(true);
    }
  }, []);

  const handleThemeSelect = (theme: 'light' | 'dark') => {
    setTheme(theme);
    localStorage.setItem('hasSelectedTheme', 'true');
    setOpen(false);
  };

  // Translations for languages not covered by the t function
  const translations = {
    title: {
      en: "Choose Your Theme",
      fr: "Choisissez Votre Thème",
      ar: "اختر المظهر الخاص بك"
    },
    description: {
      en: "Select your preferred theme for browsing Kotobcom. You can change this later in the header.",
      fr: "Sélectionnez votre thème préféré pour naviguer sur Kotobcom. Vous pourrez le modifier ultérieurement dans l'en-tête.",
      ar: "حدد المظهر المفضل لديك لتصفح كتوبكوم. يمكنك تغيير ذلك لاحقًا في الشريط العلوي."
    },
    lightMode: {
      en: "Light Mode",
      fr: "Mode Clair",
      ar: "الوضع الفاتح"
    },
    darkMode: {
      en: "Dark Mode",
      fr: "Mode Sombre",
      ar: "الوضع الداكن"
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className={`sm:max-w-md ${language === 'ar' ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-center">
            {translations.title[language as keyof typeof translations.title]}
          </DialogTitle>
          <DialogDescription className="text-center">
            {translations.description[language as keyof typeof translations.description]}
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-center gap-4 py-6">
          <Button
            variant="outline"
            size="lg"
            className="flex flex-col items-center gap-2 p-6 border-2 hover:border-primary hover:bg-secondary/50"
            onClick={() => handleThemeSelect('light')}
          >
            <Sun className="h-8 w-8 text-amber-500" />
            <span>{translations.lightMode[language as keyof typeof translations.lightMode]}</span>
          </Button>

          <Button
            variant="default"
            size="lg"
            className="flex flex-col items-center gap-2 p-6 border-2 border-[#6e191c] bg-gradient-to-br from-[#0a0a0a] via-[#0f0f0f] to-[#0a0a0a] hover:bg-gradient-to-br hover:from-[#0f0f0f] hover:via-[#141414] hover:to-[#0f0f0f] shadow-[0_0_20px_rgba(110,25,28,0.6)] hover:shadow-[0_0_30px_rgba(110,25,28,0.8)] transition-all duration-500"
            onClick={() => handleThemeSelect('dark')}
          >
            <Moon className="h-8 w-8 text-[#6e191c] drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]" />
            <span className="font-bold text-[#f0ebe0]">{translations.darkMode[language as keyof typeof translations.darkMode]}</span>
            <span className="text-xs mt-1 text-[#f0ebe0]/70">Recommended</span>
          </Button>
        </div>

        <DialogFooter className="sm:justify-center">
          <div className="text-xs text-muted-foreground text-center">
            {language === 'en' ? "You can change this setting anytime from the header" :
             language === 'fr' ? "Vous pouvez modifier ce paramètre à tout moment depuis l'en-tête" :
             "يمكنك تغيير هذا الإعداد في أي وقت من الشريط العلوي"}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ThemeSelectorModal;
