
import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCart } from '@/contexts/CartContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  Menu,
  Search,
  ShoppingCart,
  Sun,
  Moon,
  ChevronDown,
  X,
  Globe,
  MessageSquare,
  Home,
  BookOpen,
  BookMarked,
  Phone,
  Bookmark,
  Award
} from 'lucide-react';

const Header = () => {
  const { language, setLanguage, t } = useLanguage();

  // Language names for display
  const languageNames = {
    en: 'English',
    fr: 'Français',
    ar: 'العربية'
  };

  const handleLanguageChange = (lang: 'en' | 'fr' | 'ar') => {
    setLanguage(lang);
    setIsLangMenuOpen(false);
  };
  const { theme, toggleTheme } = useTheme();
  const { totalItems } = useCart();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangMenuOpen, setIsLangMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  // Add scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 30) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleLangMenu = () => {
    setIsLangMenuOpen(!isLangMenuOpen);
  };

  // Close language menu if clicked outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isLangMenuOpen && !(event.target as Element).closest('.lang-menu-container')) {
        setIsLangMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isLangMenuOpen]);

  // Check if a nav link is active
  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <header className="relative z-30">


      {/* 🌙 ULTRA PREMIUM HEADER */}
      <div className={`py-4 ${theme === 'light' ? 'bg-white' : 'bg-gradient-to-r from-[#050505] via-[#0a0a0a] to-[#050505]'} border-b ${theme === 'light' ? 'border-[#6e191c]/10' : 'border-[#6e191c]/50'} relative backdrop-blur-xl`}>
        {/* 💫 Premium Dark Mode Accents */}
        {theme === 'dark' && (
          <>
            <div className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-[#6e191c]/70 to-transparent"></div>
            <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-[#6e191c]/50 to-transparent"></div>

            {/* 🌟 Ambient Glow Effects */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#6e191c]/5 via-transparent to-[#6e191c]/5 opacity-50"></div>

            {/* ✨ Subtle Light Particles */}
            <div className="absolute top-2 left-1/4 w-1 h-1 bg-[#6e191c] rounded-full opacity-60 animate-pulse"></div>
            <div className="absolute bottom-2 right-1/3 w-0.5 h-0.5 bg-[#8a1e24] rounded-full opacity-70 animate-pulse" style={{animationDelay: '1s'}}></div>
          </>
        )}
        <div className="container">
          <div className="flex items-center justify-between">
            {/* 💎 ULTRA PREMIUM LOGO */}
            <Link to="/" className="flex items-center group">
              <div className="flex items-center">
                <div className="relative">
                  <span className="font-serif text-xl md:text-2xl font-bold relative flex items-center">
                    <span className={`${theme === 'light' ? 'text-black' : 'text-[#f0ebe0] drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]'} group-hover:text-[#6e191c] transition-all duration-500`}>
                      Kotob
                    </span>
                    <span className={`${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c] drop-shadow-[0_2px_8px_rgba(110,25,28,0.8)]'} group-hover:text-[#8a1e24] transition-all duration-500`}>
                      com
                    </span>
                  </span>

                  {/* 🌟 Enhanced Underline Animation */}
                  <span className={`absolute -bottom-1 left-0 w-0 h-1 ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-gradient-to-r from-[#6e191c] to-[#8a1e24] shadow-[0_2px_8px_rgba(110,25,28,0.6)]'} group-hover:w-full transition-all duration-500 rounded-full`}></span>

                  {/* 💫 Premium Glow Effect for Dark Mode */}
                  {theme === 'dark' && (
                    <>
                      <div className="absolute -inset-2 bg-gradient-to-r from-[#6e191c]/15 via-[#8a1e24]/10 to-[#6e191c]/15 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="absolute -inset-1 bg-gradient-to-r from-[#6e191c]/10 to-[#8a1e24]/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </>
                  )}
                </div>

                {/* 🎭 Enhanced Tagline */}
                <div className={`ml-4 pl-4 border-l-2 ${theme === 'light' ? 'border-[#6e191c]/20' : 'border-[#6e191c]/50'} hidden sm:block relative`}>
                  <span className={`text-xs font-semibold ${theme === 'light' ? 'text-black/80' : 'text-[#f0ebe0]/90 drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]'} transition-all duration-300 group-hover:text-[#6e191c]`}>
                    {language === 'en' ? "Islamic Bookstore" : language === 'fr' ? "Librairie Islamique" : "مكتبة إسلامية"}
                  </span>

                  {/* ✨ Subtle Glow for Dark Mode */}
                  {theme === 'dark' && (
                    <div className="absolute -inset-1 bg-[#6e191c]/5 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  )}
                </div>
              </div>
            </Link>

            {/* Enhanced desktop navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link
                to="/"
                className={`text-sm font-medium transition-all duration-300 relative py-2 group ${
                  isActive('/')
                    ? (theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]')
                    : (theme === 'light' ? 'text-black/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/90 hover:text-[#6e191c]')
                }`}
              >
                <div className="flex items-center relative">
                  <Home className={`h-3.5 w-3.5 mr-1.5 transition-transform duration-300 group-hover:scale-110 ${isActive('/') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                  <span>{t('home')}</span>

                  {/* Subtle highlight for active item in dark mode */}
                  {theme === 'dark' && isActive('/') && (
                    <div className="absolute -inset-1 bg-[#6e191c]/20 rounded-md -z-10"></div>
                  )}
                </div>
                <span className={`absolute bottom-0 left-0 w-full h-[2px] ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'} transform origin-left transition-transform duration-300 ${isActive('/') ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'}`}></span>
              </Link>

              <Link
                to="/books"
                className={`text-sm font-medium transition-all duration-300 relative py-2 group ${
                  isActive('/books')
                    ? (theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]')
                    : (theme === 'light' ? 'text-black/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/90 hover:text-[#6e191c]')
                }`}
              >
                <div className="flex items-center relative">
                  <BookOpen className={`h-3.5 w-3.5 mr-1.5 transition-transform duration-300 group-hover:scale-110 ${isActive('/books') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                  <span>{t('books')}</span>

                  {/* Subtle highlight for active item in dark mode */}
                  {theme === 'dark' && isActive('/books') && (
                    <div className="absolute -inset-1 bg-[#6e191c]/10 rounded-md -z-10"></div>
                  )}
                </div>
                <span className={`absolute bottom-0 left-0 w-full h-[2px] ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'} transform origin-left transition-transform duration-300 ${isActive('/books') ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'}`}></span>
              </Link>

              <Link
                to="/quran"
                className={`text-sm font-medium transition-all duration-300 relative py-2 group ${
                  isActive('/quran')
                    ? (theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]')
                    : (theme === 'light' ? 'text-black/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/90 hover:text-[#6e191c]')
                }`}
              >
                <div className="flex items-center relative">
                  <BookMarked className={`h-3.5 w-3.5 mr-1.5 transition-transform duration-300 group-hover:scale-110 ${isActive('/quran') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                  <span>{t('quran')}</span>

                  {/* Subtle highlight for active item in dark mode */}
                  {theme === 'dark' && isActive('/quran') && (
                    <div className="absolute -inset-1 bg-[#6e191c]/10 rounded-md -z-10"></div>
                  )}
                </div>
                <span className={`absolute bottom-0 left-0 w-full h-[2px] ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'} transform origin-left transition-transform duration-300 ${isActive('/quran') ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'}`}></span>
              </Link>

              <Link
                to="/categories"
                className={`text-sm font-medium transition-all duration-300 relative py-2 group ${
                  isActive('/categories')
                    ? (theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]')
                    : (theme === 'light' ? 'text-black/80 hover:text-[#6e191c]' : 'text-[#ded7ba]/90 hover:text-[#6e191c]')
                }`}
              >
                <div className="flex items-center relative">
                  <Bookmark className={`h-3.5 w-3.5 mr-1.5 transition-transform duration-300 group-hover:scale-110 ${isActive('/categories') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                  <span>{t('categories')}</span>

                  {/* Subtle highlight for active item in dark mode */}
                  {theme === 'dark' && isActive('/categories') && (
                    <div className="absolute -inset-1 bg-[#6e191c]/10 rounded-md -z-10"></div>
                  )}
                </div>
                <span className={`absolute bottom-0 left-0 w-full h-[2px] ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'} transform origin-left transition-transform duration-300 ${isActive('/categories') ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'}`}></span>
              </Link>
            </nav>

            {/* Enhanced action buttons */}
            <div className="flex items-center gap-3">
              {/* Enhanced language switcher */}
              <div className="relative lang-menu-container hidden md:block">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`${theme === 'light'
                    ? 'bg-white/90 text-black/90 hover:bg-white border border-[#6e191c]/15'
                    : 'bg-[#080808] text-[#ded7ba]/90 hover:bg-[#0d0d0d] border border-[#6e191c]/40'}
                    flex items-center gap-1.5 rounded-md py-1.5 px-3 text-xs transition-all duration-300 group`}
                  onClick={toggleLangMenu}
                >
                  <Globe className={`h-3.5 w-3.5 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} transition-transform duration-300 group-hover:rotate-12`} />
                  <span className="font-medium">{languageNames[language as keyof typeof languageNames]}</span>
                  <ChevronDown className={`h-3 w-3 transition-transform duration-300 ${isLangMenuOpen ? 'rotate-180' : ''} ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}`} />
                </Button>

                {isLangMenuOpen && (
                  <div className={`absolute top-full right-0 mt-1 ${theme === 'light'
                    ? 'bg-white'
                    : 'bg-[#080808]'} rounded-md shadow-lg border ${theme === 'light'
                    ? 'border-[#6e191c]/15'
                    : 'border-[#6e191c]/40'} z-50 min-w-[120px] overflow-hidden`}>
                    <div className="py-1">
                      {Object.keys(languageNames).map((lang) => (
                        <button
                          key={lang}
                          className={`w-full text-left px-3 py-2 text-xs flex items-center transition-all duration-300 ${language === lang ?
                            `${theme === 'light'
                              ? 'bg-[#6e191c]/5 text-[#6e191c] font-medium'
                              : 'bg-[#6e191c]/20 text-[#ded7ba] font-medium'}` :
                            `hover:${theme === 'light'
                              ? 'bg-[#6e191c]/5'
                              : 'bg-[#6e191c]/15'} ${theme === 'light' ? 'text-black/90' : 'text-[#ded7ba]/90'}`}`}
                          onClick={() => handleLanguageChange(lang as 'en' | 'fr' | 'ar')}
                        >
                          <Globe className={`h-3 w-3 mr-2 ${language === lang ? (theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]') : 'opacity-70'}`} />
                          <span>{languageNames[lang as keyof typeof languageNames]}</span>
                          {language === lang && (
                            <span className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-1.5 h-1.5 rounded-full ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'}`}></span>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Enhanced theme toggle */}
              <Button
                variant="ghost"
                size="sm"
                className={`hidden md:flex items-center ${theme === 'light'
                  ? 'bg-white/90 text-black/90 hover:bg-white border border-[#6e191c]/15'
                  : 'bg-gradient-to-br from-[#0a0a0a] to-[#0f0f0f] text-[#f0ebe0]/90 hover:bg-gradient-to-br hover:from-[#0f0f0f] hover:to-[#141414] border border-[#6e191c]/50 shadow-[0_4px_16px_rgba(110,25,28,0.2)]'}
                  rounded-md p-1.5 transition-all duration-500 group backdrop-blur-sm`}
                onClick={toggleTheme}
                aria-label={theme === 'dark' ? t('lightMode') : t('darkMode')}
              >
                {theme === 'dark' ? (
                  <Sun className={`h-3.5 w-3.5 text-[#6e191c] transition-transform duration-300 group-hover:rotate-45`} />
                ) : (
                  <Moon className={`h-3.5 w-3.5 text-[#6e191c] transition-transform duration-300 group-hover:rotate-12`} />
                )}
              </Button>

              {/* Enhanced cart button */}
              <Link to="/cart" className="relative group">
                <div className={`p-1.5 rounded-md transition-all duration-300 ${theme === 'light'
                  ? 'bg-[#6e191c] hover:bg-[#8a1e24] text-white'
                  : 'bg-[#6e191c] hover:bg-[#8a1e24] text-white'}
                  shadow-sm hover:shadow-md hover:-translate-y-0.5 relative overflow-hidden`}>

                  {/* Subtle shine effect */}
                  <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>

                  <ShoppingCart className="h-4 w-4 text-white relative z-10" />

                  {totalItems > 0 && (
                    <span className="absolute -top-1.5 -right-1.5 w-4 h-4 rounded-full flex items-center justify-center text-[10px] font-medium bg-white text-[#6e191c] border border-[#6e191c]/50 shadow-sm">
                      {totalItems}
                    </span>
                  )}
                </div>
              </Link>

              {/* Enhanced mobile menu toggle */}
              <Button
                variant="ghost"
                size="sm"
                className={`md:hidden p-1.5 rounded-md ${theme === 'light'
                  ? 'bg-[#6e191c] text-white hover:bg-[#8a1e24]'
                  : 'bg-[#6e191c] text-white hover:bg-[#8a1e24]'}
                  shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-0.5 relative overflow-hidden group`}
                onClick={toggleMenu}
                aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
              >
                {/* Subtle shine effect */}
                <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>

                {isMenuOpen ? (
                  <X className="h-4 w-4 text-white relative z-10" />
                ) : (
                  <Menu className="h-4 w-4 text-white relative z-10" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>



      {/* Enhanced mobile menu with deep dark theme */}
      {isMenuOpen && (
        <div className={`md:hidden ${theme === 'light' ? 'bg-white' : 'bg-[#080808]'} absolute w-full shadow-lg z-40 animate-fade-in border-b ${theme === 'light' ? 'border-[#6e191c]/15' : 'border-[#6e191c]/40'}`}>
          {/* Enhanced accent lines */}
          {theme === 'dark' ? (
            <>
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/40 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/50 to-transparent"></div>
            </>
          ) : (
            <div className="absolute top-0 left-0 w-full h-[1px] bg-[#6e191c]/15"></div>
          )}

          <div className="container py-4">
            {/* Enhanced mobile menu header */}
            <div className="flex items-center justify-between py-2 mb-4 border-b border-gray-100 dark:border-[#1a1a1a]">
              <span className={`text-sm font-medium ${theme === 'light' ? 'text-black/90' : 'text-[#ded7ba]/90'}`}>
                {language === 'en' ? "Menu" : language === 'fr' ? "Menu" : "القائمة"}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className={`p-1.5 rounded-md ${theme === 'light'
                  ? 'hover:bg-gray-100 text-[#6e191c]/80 hover:text-[#6e191c]'
                  : 'hover:bg-[#121212] text-[#6e191c]/90 hover:text-[#6e191c]'}
                  transition-all duration-300 hover:-translate-y-0.5`}
                onClick={() => setIsMenuOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Enhanced mobile navigation */}
            <nav className="flex flex-col space-y-1.5">
              <Link
                to="/"
                className={`flex items-center py-3 px-4 rounded-md text-sm transition-all duration-300 group
                  ${isActive('/')
                    ? (theme === 'light'
                      ? 'bg-[#6e191c]/5 text-[#6e191c]'
                      : 'bg-[#6e191c]/15 text-[#6e191c]')
                    : (theme === 'light'
                      ? 'text-black/90 hover:bg-[#6e191c]/5 hover:text-[#6e191c]'
                      : 'text-[#ded7ba]/90 hover:bg-[#6e191c]/10 hover:text-[#6e191c]')
                  }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Home className={`h-4 w-4 mr-3 transition-transform duration-300 group-hover:scale-110 ${isActive('/') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                <span>{t('home')}</span>
                {isActive('/') && (
                  <span className={`ml-auto w-1.5 h-1.5 rounded-full ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'}`}></span>
                )}
              </Link>

              <Link
                to="/books"
                className={`flex items-center py-3 px-4 rounded-md text-sm transition-all duration-300 group
                  ${isActive('/books')
                    ? (theme === 'light'
                      ? 'bg-[#6e191c]/5 text-[#6e191c]'
                      : 'bg-[#6e191c]/15 text-[#6e191c]')
                    : (theme === 'light'
                      ? 'text-black/90 hover:bg-[#6e191c]/5 hover:text-[#6e191c]'
                      : 'text-[#ded7ba]/90 hover:bg-[#6e191c]/10 hover:text-[#6e191c]')
                  }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <BookOpen className={`h-4 w-4 mr-3 transition-transform duration-300 group-hover:scale-110 ${isActive('/books') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                <span>{t('books')}</span>
                {isActive('/books') && (
                  <span className={`ml-auto w-1.5 h-1.5 rounded-full ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'}`}></span>
                )}
              </Link>

              <Link
                to="/quran"
                className={`flex items-center py-3 px-4 rounded-md text-sm transition-all duration-300 group
                  ${isActive('/quran')
                    ? (theme === 'light'
                      ? 'bg-[#6e191c]/5 text-[#6e191c]'
                      : 'bg-[#6e191c]/15 text-[#6e191c]')
                    : (theme === 'light'
                      ? 'text-black/90 hover:bg-[#6e191c]/5 hover:text-[#6e191c]'
                      : 'text-[#ded7ba]/90 hover:bg-[#6e191c]/10 hover:text-[#6e191c]')
                  }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <BookMarked className={`h-4 w-4 mr-3 transition-transform duration-300 group-hover:scale-110 ${isActive('/quran') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                <span>{t('quran')}</span>
                {isActive('/quran') && (
                  <span className={`ml-auto w-1.5 h-1.5 rounded-full ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'}`}></span>
                )}
              </Link>

              <Link
                to="/categories"
                className={`flex items-center py-3 px-4 rounded-md text-sm transition-all duration-300 group
                  ${isActive('/categories')
                    ? (theme === 'light'
                      ? 'bg-[#6e191c]/5 text-[#6e191c]'
                      : 'bg-[#6e191c]/15 text-[#6e191c]')
                    : (theme === 'light'
                      ? 'text-black/90 hover:bg-[#6e191c]/5 hover:text-[#6e191c]'
                      : 'text-[#ded7ba]/90 hover:bg-[#6e191c]/10 hover:text-[#6e191c]')
                  }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Bookmark className={`h-4 w-4 mr-3 transition-transform duration-300 group-hover:scale-110 ${isActive('/categories') && theme === 'dark' ? 'text-[#6e191c]' : ''}`} />
                <span>{t('categories')}</span>
                {isActive('/categories') && (
                  <span className={`ml-auto w-1.5 h-1.5 rounded-full ${theme === 'light' ? 'bg-[#6e191c]' : 'bg-[#6e191c]'}`}></span>
                )}
              </Link>
            </nav>

            {/* Enhanced mobile actions */}
            <div className="mt-6 pt-4 border-t border-gray-100 dark:border-[#1a1a1a] flex items-center justify-between">
              {/* Enhanced language switcher */}
              <div className="flex items-center space-x-2">
                {Object.keys(languageNames).map((lang) => (
                  <button
                    key={lang}
                    className={`px-3 py-1.5 text-xs rounded-md transition-all duration-300 ${
                      language === lang
                        ? (theme === 'light'
                          ? 'bg-[#6e191c] text-white shadow-sm'
                          : 'bg-[#6e191c] text-white shadow-sm')
                        : (theme === 'light'
                          ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:-translate-y-0.5'
                          : 'bg-[#0d0d0d] text-[#ded7ba]/90 hover:bg-[#121212] hover:-translate-y-0.5')
                    }`}
                    onClick={() => handleLanguageChange(lang as 'en' | 'fr' | 'ar')}
                  >
                    {lang.toUpperCase()}
                  </button>
                ))}
              </div>

              {/* Enhanced theme toggle */}
              <Button
                variant="outline"
                size="sm"
                className={`p-2 rounded-md transition-all duration-500 hover:-translate-y-0.5 hover:scale-110 ${
                  theme === 'light'
                    ? 'bg-gray-100 text-[#6e191c] hover:bg-gray-200 border-gray-200'
                    : 'bg-gradient-to-br from-[#0a0a0a] to-[#0f0f0f] text-[#6e191c] hover:bg-gradient-to-br hover:from-[#0f0f0f] hover:to-[#141414] border-[#6e191c]/30 shadow-[0_4px_16px_rgba(110,25,28,0.3)]'
                }`}
                onClick={toggleTheme}
                aria-label={theme === 'dark' ? t('lightMode') : t('darkMode')}
              >
                {theme === 'light' ? (
                  <Moon className="h-4 w-4" />
                ) : (
                  <Sun className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
