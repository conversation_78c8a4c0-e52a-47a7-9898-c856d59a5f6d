
import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { Toaster } from 'sonner';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { CartProvider } from '@/contexts/CartContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ThemeSelectorModal from '@/components/ThemeSelectorModal';
import Home from '@/pages/Home';
import BooksPage from '@/pages/BooksPage';
import QuranPage from '@/pages/QuranPage';
import BookDetail from '@/pages/BookDetail';
import QuranDetail from '@/pages/QuranDetail';
import CartPage from '@/pages/CartPage';
import CheckoutPage from '@/pages/CheckoutPage';
import OrderConfirmation from '@/pages/OrderConfirmation';
import ContactPage from '@/pages/ContactPage';
import Categories from '@/pages/Categories';
import CategoryPage from '@/pages/CategoryPage';
import AdminLogin from '@/pages/AdminLogin';
import AdminDashboard from '@/pages/AdminDashboard';
import AdminBooks from '@/pages/admin/AdminBooks';
import AdminOrders from '@/pages/admin/AdminOrders';
import NotFound from '@/pages/NotFound';
import { Toaster as ShadcnToaster } from '@/components/ui/toaster';
import HiddenAdminButton from '@/components/HiddenAdminButton';
import AdminGuard from '@/components/AdminGuard';
import { logConnectionStatus } from '@/utils/connectionTest';

// ScrollToTop component to handle scroll on page navigation
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

// Component to remove triangular element at the bottom of the page
const RemoveTriangleElement = () => {
  useEffect(() => {
    // Function to remove triangular elements
    const removeTriangleElements = () => {
      // Look for elements that might be the triangular shape with red icon
      const possibleSelectors = [
        // Common class names for such elements
        '.triangle-bottom-element',
        '.bottom-triangle-container',
        '.bottom-arrow-container',
        // Elements with specific styles that might create triangles
        'div[style*="transform: rotate(45deg)"]',
        'div[style*="border-left: transparent"]',
        'div[style*="border-right: transparent"]',
        'div[style*="border-bottom: transparent"]',
        // SVG elements that might be triangular
        'svg[viewBox*="0 0 1200 120"]',
        // Any element with a red icon at the bottom
        'div.fixed[style*="bottom"]',
        // Any element with a triangular shape at the bottom
        'div[style*="clip-path: polygon"]'
      ];

      // Try to find and remove elements matching these selectors
      possibleSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach(el => {
            if (el.parentNode) {
              el.parentNode.removeChild(el);
            }
          });
        } catch (error) {
          console.error(`Error removing elements with selector ${selector}:`, error);
        }
      });

      // Also look for any fixed positioned elements at the bottom
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        const style = window.getComputedStyle(el);
        if (
          style.position === 'fixed' &&
          (style.bottom === '0px' || parseInt(style.bottom) < 50) &&
          el.tagName !== 'BUTTON' && // Don't remove actual UI buttons
          !el.classList.contains('toaster') // Don't remove toast notifications
        ) {
          // Check if it has a triangular shape or red color
          const backgroundColor = style.backgroundColor;
          const borderColor = style.borderColor;
          const transform = style.transform;

          if (
            backgroundColor.includes('rgb(110, 25, 28)') || // Burgundy color
            backgroundColor.includes('rgb(138, 30, 36)') || // Burgundy light color
            borderColor.includes('rgb(110, 25, 28)') ||
            transform.includes('rotate') ||
            el.innerHTML.includes('svg')
          ) {
            if (el.parentNode) {
              el.parentNode.removeChild(el);
            }
          }
        }
      });
    };

    // Run once on mount
    removeTriangleElements();

    // Also run on any DOM changes
    const observer = new MutationObserver(removeTriangleElements);
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => observer.disconnect();
  }, []);

  return null;
};

function App() {
  // Test Supabase connection on app startup
  useEffect(() => {
    logConnectionStatus();
  }, []);

  return (
    <ThemeProvider>
      <LanguageProvider>
        <CartProvider>
          <Router>
            <ScrollToTop />
            <RemoveTriangleElement />
            <div className="flex flex-col min-h-screen bg-background">
              <Header />
              <ThemeSelectorModal />
              <div className="flex-1">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/books" element={<BooksPage />} />
                  <Route path="/quran" element={<QuranPage />} />
                  <Route path="/books/:id" element={<BookDetail />} />
                  <Route path="/quran/:id" element={<QuranDetail />} />
                  <Route path="/categories" element={<Categories />} />
                  <Route path="/categories/:categoryId" element={<CategoryPage />} />
                  <Route path="/bestsellers" element={<BooksPage />} />
                  <Route path="/cart" element={<CartPage />} />
                  <Route path="/checkout" element={<CheckoutPage />} />
                  <Route path="/order-confirmation" element={<OrderConfirmation />} />
                  <Route path="/contact" element={<ContactPage />} />
                  <Route path="/admin" element={<AdminLogin />} />
                  <Route path="/admin/dashboard" element={<AdminGuard><AdminDashboard /></AdminGuard>} />
                  <Route path="/admin/books" element={<AdminGuard><AdminBooks /></AdminGuard>} />
                  <Route path="/admin/orders" element={<AdminGuard><AdminOrders /></AdminGuard>} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </div>
              <Footer />
              <HiddenAdminButton />
            </div>
            <Toaster
              position="top-right"
              richColors
              closeButton
              toastOptions={{
                className: "shadow-lg border border-border",
                style: {
                  background: 'var(--background)',
                  color: 'var(--foreground)',
                }
              }}
            />
            <ShadcnToaster />
          </Router>
        </CartProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
}

export default App;
