
export interface ReviewData {
  id: string;
  user: string;
  comment: string;
  rating: number;
  createdAt: Date;
}

export interface BookData {
  id: string;
  title: string;
  author: string;
  description: string;
  price: number;
  coverImage: string;
  language: string;
  category: string;
  publisher: string;
  inStock: boolean;
  rating: number;
  reviews: ReviewData[];
  featured: boolean;
  newArrival: boolean;
  bestSeller: boolean;
}

export const booksData: BookData[] = [
  {
    id: '1',
    title: 'The Great Gatsby',
    author: '<PERSON><PERSON>',
    description: 'A novel about the Roaring Twenties.',
    price: 10.99,
    coverImage: 'https://example.com/gatsby.jpg',
    language: 'English',
    category: 'Fiction',
    publisher: 'Scribner',
    inStock: true,
    rating: 4.5,
    reviews: [],
    featured: true,
    newArrival: true,
    bestSeller: true,
  },
  {
    id: '2',
    title: 'To Kill a Mockingbird',
    author: 'Harper Lee',
    description: 'A classic novel set in the American South.',
    price: 12.50,
    coverImage: 'https://example.com/mockingbird.jpg',
    language: 'English',
    category: 'Fiction',
    publisher: 'J. B. Lippincott & Co.',
    inStock: true,
    rating: 4.8,
    reviews: [],
    featured: true,
    newArrival: false,
    bestSeller: true,
  },
  {
    id: '3',
    title: '1984',
    author: 'George Orwell',
    description: 'A dystopian novel about totalitarianism.',
    price: 9.99,
    coverImage: 'https://example.com/1984.jpg',
    language: 'English',
    category: 'Science Fiction',
    publisher: 'Secker & Warburg',
    inStock: true,
    rating: 4.6,
    reviews: [],
    featured: false,
    newArrival: false,
    bestSeller: false,
  },
  {
    id: '4',
    title: 'Pride and Prejudice',
    author: 'Jane Austen',
    description: 'A romantic novel set in rural England.',
    price: 11.25,
    coverImage: 'https://example.com/pride.jpg',
    language: 'English',
    category: 'Romance',
    publisher: 'T. Egerton',
    inStock: true,
    rating: 4.7,
    reviews: [],
    featured: false,
    newArrival: false,
    bestSeller: false,
  },
  {
    id: '5',
    title: 'The Little Prince',
    author: 'Antoine de Saint-Exupéry',
    description: 'A philosophical tale about a young prince.',
    price: 8.75,
    coverImage: 'https://example.com/prince.jpg',
    language: 'French',
    category: 'Children',
    publisher: 'Reynal & Hitchcock',
    inStock: true,
    rating: 4.9,
    reviews: [],
    featured: true,
    newArrival: true,
    bestSeller: true,
  },
  {
    id: '6',
    title: 'Don Quixote',
    author: 'Miguel de Cervantes',
    description: 'A novel about a man who sets out to revive chivalry.',
    price: 13.50,
    coverImage: 'https://example.com/quixote.jpg',
    language: 'Spanish',
    category: 'Fiction',
    publisher: 'Francisco de Robles',
    inStock: true,
    rating: 4.4,
    reviews: [],
    featured: false,
    newArrival: false,
    bestSeller: false,
  },
  {
    id: '7',
    title: 'One Hundred Years of Solitude',
    author: 'Gabriel García Márquez',
    description: 'A multi-generational story of the Buendía family.',
    price: 14.99,
    coverImage: 'https://example.com/years.jpg',
    language: 'Spanish',
    category: 'Fiction',
    publisher: 'Editorial Sudamericana',
    inStock: true,
    rating: 4.8,
    reviews: [],
    featured: true,
    newArrival: false,
    bestSeller: true,
  },
  {
    id: '8',
    title: 'Moby-Dick',
    author: 'Herman Melville',
    description: 'A novel about a whaling voyage.',
    price: 11.75,
    coverImage: 'https://example.com/moby.jpg',
    language: 'English',
    category: 'Adventure',
    publisher: 'Richard Bentley',
    inStock: true,
    rating: 4.6,
    reviews: [],
    featured: false,
    newArrival: false,
    bestSeller: false,
  },
];

// Export the initial books data for seeding
export const INITIAL_BOOKS = [
  {
    title: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    description: 'A novel about the Roaring Twenties.',
    price: 10.99,
    cover_image: 'https://example.com/gatsby.jpg',
    language: 'English',
    category: 'Fiction',
    publisher: 'Scribner',
    in_stock: true,
    rating: 4.5,
    featured: true,
    new_arrival: true,
    best_seller: true,
  },
  {
    title: 'To Kill a Mockingbird',
    author: 'Harper Lee',
    description: 'A classic novel set in the American South.',
    price: 12.50,
    cover_image: 'https://example.com/mockingbird.jpg',
    language: 'English',
    category: 'Fiction',
    publisher: 'J. B. Lippincott & Co.',
    in_stock: true,
    rating: 4.8,
    featured: true,
    new_arrival: false,
    best_seller: true,
  },
  {
    title: '1984',
    author: 'George Orwell',
    description: 'A dystopian novel about totalitarianism.',
    price: 9.99,
    cover_image: 'https://example.com/1984.jpg',
    language: 'English',
    category: 'Science Fiction',
    publisher: 'Secker & Warburg',
    in_stock: true,
    rating: 4.6,
    featured: false,
    new_arrival: false,
    best_seller: false,
  },
];

export const INITIAL_QURAN_BOOKS = [
  {
    title: 'The Holy Quran - English Translation',
    author: 'Various Scholars',
    description: 'English translation of the Holy Quran with commentary.',
    price: 15.99,
    cover_image: 'https://example.com/quran-english.jpg',
    language: 'English',
    publisher: 'Islamic Foundation',
    in_stock: true,
    rating: 5.0,
    featured: true,
    new_arrival: true,
    best_seller: true,
  },
  {
    title: 'Tajweed Quran - Arabic',
    author: 'Traditional',
    description: 'Arabic Quran with Tajweed rules color-coded for proper recitation.',
    price: 25.99,
    cover_image: 'https://example.com/tajweed-quran.jpg',
    language: 'Arabic',
    publisher: 'Dar Al-Maarifah',
    in_stock: true,
    rating: 4.9,
    featured: true,
    new_arrival: false,
    best_seller: true,
  },
  {
    title: 'The Clear Quran - Bilingual Edition',
    author: 'Dr. Mustafa Khattab',
    description: 'Modern English-Arabic bilingual translation of the Quran.',
    price: 19.99,
    cover_image: 'https://example.com/clear-quran.jpg',
    language: 'English/Arabic',
    publisher: 'Al-Furqan Publishing',
    in_stock: true,
    rating: 4.8,
    featured: false,
    new_arrival: true,
    best_seller: false,
  },
];
