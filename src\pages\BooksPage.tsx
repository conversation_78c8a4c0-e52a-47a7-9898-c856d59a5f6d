
import React, { useState, useEffect, useRef } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Link } from 'react-router-dom';
import BookCard from '@/components/BookCard';
import { BookData } from '@/data/books';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { fetchBooks } from '@/services/bookService';
import BreadcrumbNav from '@/components/BreadcrumbNav';
import {
  BookOpen,
  Filter,
  Search,
  ChevronDown,
  ChevronRight,
  BookText,
  BookMarked,
  Bookmark,
  SlidersHorizontal,
  X,
  Languages
} from 'lucide-react';

const BooksPage = () => {
  const { t, language } = useLanguage();
  const { theme } = useTheme();
  const isRTL = language === 'ar';

  // State for books and filters
  const [allBooks, setAllBooks] = useState<BookData[]>([]);
  const [filteredBooks, setFilteredBooks] = useState<BookData[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [languageFilter, setLanguageFilter] = useState<string>('all');
  const [authorFilter, setAuthorFilter] = useState<string>('all');
  const [priceRangeFilter, setPriceRangeFilter] = useState<[number, number]>([0, 1000]);
  const [sortBy, setSortBy] = useState<string>('default');
  const [isLoading, setIsLoading] = useState(true);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Refs for scroll animations
  const popularRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadBooks = async () => {
      try {
        setIsLoading(true);
        const books = await fetchBooks();

        // Filter out null books and books with Quran category
        const validBooks = (books || []).filter(book => book && book.category !== "Quran");

        setAllBooks(validBooks);
        setFilteredBooks(validBooks);
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading books:', error);
        setIsLoading(false);
      }
    };

    loadBooks();
  }, []);

  // Get unique categories and languages with null checks
  const categories = [...new Set(allBooks
    .filter(book => book && book.category && book.category !== "Quran")
    .map(book => book.category))];

  const languages = [...new Set(allBooks
    .filter(book => book && book.language)
    .map(book => book.language))];

  // Get unique authors
  const authors = [...new Set(allBooks
    .filter(book => book && book.author)
    .map(book => book.author))];

  // Get min and max prices
  const prices = allBooks
    .filter(book => book && typeof book.price === 'number')
    .map(book => book.price as number);
  const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
  const maxPrice = prices.length > 0 ? Math.max(...prices) : 1000;

  // Popular books
  const popularBooks = allBooks
    .filter(book => book && book.bestSeller)
    .slice(0, 4);

  // Handle filter changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    applyFilters(value, categoryFilter, languageFilter, authorFilter, priceRangeFilter, sortBy);
  };

  const handleCategoryChange = (value: string) => {
    setCategoryFilter(value);
    applyFilters(searchQuery, value, languageFilter, authorFilter, priceRangeFilter, sortBy);
  };

  const handleLanguageChange = (value: string) => {
    setLanguageFilter(value);
    applyFilters(searchQuery, categoryFilter, value, authorFilter, priceRangeFilter, sortBy);
  };

  const handleAuthorChange = (value: string) => {
    setAuthorFilter(value);
    applyFilters(searchQuery, categoryFilter, languageFilter, value, priceRangeFilter, sortBy);
  };

  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRangeFilter(range);
    applyFilters(searchQuery, categoryFilter, languageFilter, authorFilter, range, sortBy);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    applyFilters(searchQuery, categoryFilter, languageFilter, authorFilter, priceRangeFilter, value);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setCategoryFilter('all');
    setLanguageFilter('all');
    setAuthorFilter('all');
    setPriceRangeFilter([minPrice, maxPrice]);
    setSortBy('default');
    setFilteredBooks(allBooks);
    setShowMobileFilters(false);
  };

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, categoryFilter, languageFilter, authorFilter, priceRangeFilter, sortBy]);

  // Apply all filters
  const applyFilters = (
    search: string,
    category: string,
    lang: string,
    author: string,
    priceRange: [number, number],
    sort: string
  ) => {
    if (!allBooks || allBooks.length === 0) {
      setFilteredBooks([]);
      return;
    }

    let result = [...allBooks];

    // Apply search filter
    if (search.trim() !== '') {
      const searchLower = search.toLowerCase();
      result = result.filter(book =>
        book &&
        (
          (book.title && book.title.toLowerCase().includes(searchLower)) ||
          (book.author && book.author.toLowerCase().includes(searchLower)) ||
          (book.description && book.description.toLowerCase().includes(searchLower))
        )
      );
    }

    // Apply category filter
    if (category !== 'all') {
      result = result.filter(book => book && book.category === category);
    }

    // Apply language filter
    if (lang !== 'all') {
      result = result.filter(book => book && book.language === lang);
    }

    // Apply author filter
    if (author !== 'all') {
      result = result.filter(book => book && book.author === author);
    }

    // Apply price range filter
    result = result.filter(book =>
      book &&
      typeof book.price === 'number' &&
      book.price >= priceRange[0] &&
      book.price <= priceRange[1]
    );

    // Apply sorting
    switch (sort) {
      case 'priceAsc':
        result.sort((a, b) => (a?.price || 0) - (b?.price || 0));
        break;
      case 'priceDesc':
        result.sort((a, b) => (b?.price || 0) - (a?.price || 0));
        break;
      case 'nameAsc':
        result.sort((a, b) => (a?.title || '').localeCompare(b?.title || ''));
        break;
      case 'nameDesc':
        result.sort((a, b) => (b?.title || '').localeCompare(a?.title || ''));
        break;
      case 'newest':
        result.sort((a, b) => (b?.publishDate || 0) - (a?.publishDate || 0));
        break;
      case 'popular':
        result.sort((a, b) => (b?.rating || 0) - (a?.rating || 0));
        break;
      default:
        // Default sorting - keep as is
        break;
    }

    setFilteredBooks(result);
  };

  const breadcrumbItems = [
    { label: t('home'), path: '/' },
    { label: t('books') }
  ];

  return (
    <main className={`${theme === 'light' ? 'bg-white' : 'bg-gradient-to-b from-[#050505] via-[#0a0a0a] to-[#050505]'} transition-all duration-500`}>
      <BreadcrumbNav items={breadcrumbItems} />

      {/* 🌙 ULTRA PREMIUM HERO SECTION */}
      <section className="relative overflow-hidden">
        {/* 💫 Spectacular Background System */}
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1507842217343-583bb7270b66?q=80&w=2000"
            alt="Books Background"
            className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
          />
          {/* 🎭 Ultra Premium Overlay System */}
          <div className={`absolute inset-0 ${theme === 'light' ? 'bg-gradient-to-br from-[#6e191c]/85 via-[#8a1e24]/90 to-[#6e191c]/85' : 'bg-gradient-to-br from-[#050505]/95 via-[#0a0a0a]/90 to-[#050505]/95'}`}></div>
          <div className={`absolute inset-0 ${theme === 'light' ? 'bg-gradient-to-b from-[#6e191c]/80 via-transparent to-white/20' : 'bg-gradient-to-b from-[#050505]/90 via-[#0a0a0a]/60 to-[#050505]/90'}`}></div>

          {/* 🌟 Majestic Decorative Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className={`absolute top-0 right-0 w-[600px] h-[600px] ${theme === 'light' ? 'bg-white/10' : 'bg-gradient-radial from-[#6e191c]/25 via-[#6e191c]/15 to-transparent'} rounded-full blur-3xl animate-pulse-slow`}></div>
            <div className={`absolute bottom-0 left-0 w-[500px] h-[500px] ${theme === 'light' ? 'bg-white/8' : 'bg-gradient-radial from-[#8a1e24]/20 via-[#6e191c]/10 to-transparent'} rounded-full blur-3xl animate-floating`}></div>
            <div className={`absolute top-1/3 left-1/3 w-[400px] h-[400px] ${theme === 'light' ? 'bg-white/5' : 'bg-gradient-radial from-[#6e191c]/15 to-transparent'} rounded-full blur-3xl`}></div>

            {/* 💎 Sophisticated Geometric Patterns */}
            <div className={`absolute top-24 right-24 w-40 h-40 border ${theme === 'light' ? 'border-white/20' : 'border-[#6e191c]/30'} rounded-full opacity-50 animate-spin-slow`}>
              <div className={`absolute inset-4 border ${theme === 'light' ? 'border-white/15' : 'border-[#6e191c]/25'} rounded-full`}>
                <div className={`absolute inset-4 border ${theme === 'light' ? 'border-white/10' : 'border-[#6e191c]/20'} rounded-full`}></div>
              </div>
            </div>
            <div className={`absolute bottom-32 left-20 w-32 h-32 border ${theme === 'light' ? 'border-white/15' : 'border-[#6e191c]/25'} rounded-full opacity-40`}>
              <div className={`absolute inset-2 ${theme === 'light' ? 'bg-white/5' : 'bg-gradient-to-br from-[#6e191c]/15 to-transparent'} rounded-full blur-sm`}></div>
            </div>

            {/* ✨ Mystical Light Particles */}
            {theme === 'dark' && (
              <>
                <div className="absolute top-20 left-1/4 w-3 h-3 bg-[#6e191c] rounded-full opacity-70 animate-pulse shadow-lg shadow-[#6e191c]/50"></div>
                <div className="absolute bottom-40 right-1/3 w-2 h-2 bg-[#8a1e24] rounded-full opacity-80 animate-pulse shadow-lg shadow-[#8a1e24]/50" style={{animationDelay: '1.5s'}}></div>
                <div className="absolute top-2/3 left-1/3 w-2.5 h-2.5 bg-[#6e191c] rounded-full opacity-75 animate-pulse shadow-lg shadow-[#6e191c]/50" style={{animationDelay: '2.5s'}}></div>
              </>
            )}
          </div>

          {/* 🔥 Enhanced Gradient Borders */}
          <div className={`absolute top-0 left-0 w-full h-[3px] ${theme === 'light' ? 'bg-gradient-to-r from-transparent via-white/30 to-transparent' : 'bg-gradient-to-r from-transparent via-[#6e191c]/60 to-transparent'}`}></div>
          <div className={`absolute bottom-0 left-0 w-full h-[3px] ${theme === 'light' ? 'bg-gradient-to-r from-transparent via-white/30 to-transparent' : 'bg-gradient-to-r from-transparent via-[#6e191c]/60 to-transparent'}`}></div>
        </div>

        {/* Enhanced Content */}
        <div className="container relative z-10 pt-20 pb-24 md:pt-28 md:pb-32">
          <div className={`max-w-4xl mx-auto text-center ${isRTL ? 'rtl' : ''}`}>
            <Badge className={`mb-6 px-5 py-2 text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg ${theme === 'light' ? 'bg-white/95 text-[#6e191c] shadow-[0_4px_15px_rgba(255,255,255,0.2)] backdrop-blur-sm' : 'bg-[#6e191c]/90 text-white shadow-[0_4px_15px_rgba(110,25,28,0.3)] backdrop-blur-sm'}`}>
              {language === 'en' ? "Book Collection" :
               language === 'fr' ? "Collection de Livres" :
               "مجموعة الكتب"}
            </Badge>

            <h1 className="text-4xl md:text-5xl lg:text-7xl font-serif font-bold mb-8 text-white leading-tight tracking-tight">
              {language === 'en' ? "Explore Our Book Collection" :
               language === 'fr' ? "Explorez Notre Collection de Livres" :
               "استكشف مجموعة كتبنا"}
            </h1>

            {/* Enhanced decorative underline */}
            <div className={`h-1.5 w-32 mx-auto rounded-full mb-8 ${theme === 'light' ? 'bg-gradient-to-r from-white/40 to-white/20' : 'bg-gradient-to-r from-[#6e191c]/60 to-[#6e191c]/30'}`}></div>

            <p className="text-xl md:text-2xl text-white/95 mb-12 max-w-3xl mx-auto leading-relaxed font-medium">
              {language === 'en' ? "Discover our extensive collection of books covering various topics, genres, and languages." :
               language === 'fr' ? "Découvrez notre vaste collection de livres couvrant divers sujets, genres et langues." :
               "اكتشف مجموعتنا الواسعة من الكتب التي تغطي مواضيع وأنواع ولغات مختلفة."}
            </p>

            {/* Enhanced Search Bar */}
            <div className={`max-w-2xl mx-auto relative group`}>
              <div className={`${theme === 'light' ? 'bg-white/95 shadow-[0_8px_30px_rgba(255,255,255,0.2)]' : 'bg-[#2a2a2a]/95 shadow-[0_8px_30px_rgba(110,25,28,0.2)]'} rounded-2xl p-2 backdrop-blur-md transition-all duration-300 group-hover:shadow-xl group-hover:scale-[1.02]`}>
                <form onSubmit={(e) => {
                  e.preventDefault();

                  // Apply filters first
                  applyFilters(searchQuery, categoryFilter, languageFilter, authorFilter, priceRangeFilter, sortBy);

                  // Check if there's only one result after filtering
                  const results = allBooks.filter(book =>
                    book &&
                    (book.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     book.author?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     book.description?.toLowerCase().includes(searchQuery.toLowerCase()))
                  );

                  // If there's exactly one result, redirect to that book's detail page
                  if (results.length === 1 && searchQuery.trim() !== '') {
                    window.location.href = `/books/${results[0].id}`;
                  }
                }} className="flex items-center">
                  <div className="relative flex-grow">
                    <Search className={`absolute left-5 top-1/2 transform -translate-y-1/2 h-5 w-5 ${theme === 'light' ? 'text-[#6e191c]/60' : 'text-white/60'} transition-all duration-300 group-hover:scale-110`} />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      placeholder={language === 'en' ? "Search books, authors, topics..." :
                                   language === 'fr' ? "Rechercher des livres, auteurs, sujets..." :
                                   "البحث عن الكتب والمؤلفين والمواضيع..."}
                      className={`w-full pl-14 pr-6 py-4 bg-transparent border-none focus:outline-none text-base font-medium ${theme === 'light' ? 'text-[#6e191c] placeholder:text-[#6e191c]/60' : 'text-white placeholder:text-white/60'}`}
                    />
                  </div>
                  <button
                    type="submit"
                    className={`px-8 py-4 rounded-xl font-semibold text-base transition-all duration-300 relative overflow-hidden group/btn ${theme === 'light' ? 'bg-[#6e191c] hover:bg-[#8a1e24] text-white shadow-[0_4px_15px_rgba(110,25,28,0.3)]' : 'bg-[#6e191c] hover:bg-[#8a1e24] text-white shadow-[0_4px_15px_rgba(110,25,28,0.4)]'} hover:scale-105 hover:shadow-xl`}
                  >
                    {/* Shine effect */}
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/15 to-transparent translate-x-[-100%] group-hover/btn:translate-x-[100%] transition-transform duration-1000"></div>

                    <span className="relative z-10 tracking-wide">{t('search')}</span>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 🌙 ULTRA PREMIUM MAIN CATALOG SECTION */}
      <section className={`py-24 relative overflow-hidden ${theme === 'light' ? 'bg-gradient-to-b from-white to-[#fafafa]' : 'bg-gradient-to-br from-[#050505] via-[#0a0a0a] to-[#050505]'}`}>
        {/* 💫 Ambient Background Effects for Dark Mode */}
        {theme === 'dark' && (
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            <div className="absolute top-0 right-1/4 w-[600px] h-[300px] bg-gradient-radial from-[#6e191c]/15 via-[#6e191c]/8 to-transparent rounded-full blur-3xl opacity-60 animate-pulse-slow"></div>
            <div className="absolute bottom-0 left-1/4 w-[500px] h-[250px] bg-gradient-radial from-[#8a1e24]/12 via-[#6e191c]/6 to-transparent rounded-full blur-3xl opacity-50 animate-floating"></div>

            {/* 💎 Crystalline Grid Pattern */}
            <div className="absolute inset-0 opacity-3" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, #6e191c 1px, transparent 0)`,
              backgroundSize: '60px 60px'
            }}></div>
          </div>
        )}

        <div className="container relative z-10">
          <div className="mb-16">
            <Badge className={`mb-6 px-6 py-3 text-sm font-bold transition-all duration-500 hover:scale-110 hover:shadow-2xl ${theme === 'light' ? 'bg-[#6e191c]/10 text-[#6e191c] border border-[#6e191c]/20 shadow-[0_4px_15px_rgba(110,25,28,0.1)]' : 'bg-gradient-to-r from-[#6e191c]/25 to-[#8a1e24]/20 text-[#f0ebe0] border border-[#6e191c]/50 shadow-[0_8px_30px_rgba(110,25,28,0.4)] backdrop-blur-sm'}`}>
              {language === 'en' ? "Browse" : language === 'fr' ? "Parcourir" : "تصفح"}
            </Badge>
            <h2 className={`text-4xl md:text-5xl font-serif font-bold tracking-tight ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#f0ebe0] drop-shadow-[0_4px_8px_rgba(0,0,0,0.8)]'}`}>
              {language === 'en' ? "Explore Our Book Collection" :
               language === 'fr' ? "Explorez notre collection de livres" :
               "استكشف مجموعة كتبنا"}
            </h2>
            {/* 💎 Premium Decorative Underline */}
            <div className={`h-2 w-32 rounded-full mt-6 relative ${theme === 'light' ? 'bg-gradient-to-r from-[#6e191c]/30 to-[#6e191c]/10' : 'bg-gradient-to-r from-[#6e191c] via-[#8a1e24] to-[#6e191c] shadow-[0_4px_16px_rgba(110,25,28,0.6)]'}`}>
              {theme === 'dark' && (
                <div className="absolute inset-0 bg-gradient-to-r from-[#6e191c] via-[#8a1e24] to-[#6e191c] rounded-full blur-sm opacity-50"></div>
              )}
            </div>
          </div>

          <div className={`flex flex-col md:flex-row gap-8 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            {/* 🌙 ULTRA PREMIUM FILTERS SIDEBAR */}
            <div className="w-full md:w-1/4">
              <div className={`${theme === 'light' ? 'bg-white shadow-[0_8px_30px_rgba(110,25,28,0.08)]' : 'bg-gradient-to-br from-[#0a0a0a] via-[#0f0f0f] to-[#0a0a0a] shadow-[0_12px_40px_rgba(0,0,0,0.8),0_8px_30px_rgba(110,25,28,0.3)]'} p-10 rounded-3xl sticky top-6 border ${theme === 'light' ? 'border-[#6e191c]/10' : 'border-[#6e191c]/30'} transition-all duration-500 hover:shadow-2xl backdrop-blur-sm relative overflow-hidden`}>
                {/* 💫 Premium Background Glow for Dark Mode */}
                {theme === 'dark' && (
                  <>
                    <div className="absolute inset-0 bg-gradient-to-br from-[#6e191c]/10 via-transparent to-[#8a1e24]/10 opacity-50"></div>
                    <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-[#6e191c]/50 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/30 to-transparent"></div>
                  </>
                )}

                <div className="flex items-center justify-between mb-10 relative z-10">
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-xl ${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-gradient-to-br from-[#6e191c]/25 to-[#8a1e24]/20 shadow-[0_4px_16px_rgba(110,25,28,0.4)]'} transition-all duration-500 hover:scale-125 hover:rotate-12`}>
                      <Filter className={`h-6 w-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#f0ebe0] drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]'}`} />
                    </div>
                    <h2 className={`text-2xl font-bold ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#f0ebe0] drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]'}`}>
                      {t('filters')}
                    </h2>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`text-sm font-bold transition-all duration-500 hover:scale-110 relative overflow-hidden ${theme === 'light' ? 'text-[#6e191c] hover:bg-[#6e191c]/10' : 'text-[#f0ebe0] hover:bg-[#6e191c]/20 border border-[#6e191c]/30 hover:border-[#6e191c]/50'}`}
                    onClick={clearFilters}
                  >
                    {/* 🌟 Shine Effect for Dark Mode */}
                    {theme === 'dark' && (
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-700"></div>
                    )}
                    <span className="relative z-10">{t('clearAll')}</span>
                  </Button>
                </div>

                <div className="space-y-8">
                  {/* Enhanced Category Filter */}
                  <div>
                    <Label htmlFor="category-filter" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                      {t('category')}
                    </Label>
                    <Select value={categoryFilter} onValueChange={handleCategoryChange}>
                      <SelectTrigger id="category-filter" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                        <SelectValue placeholder={t('allCategories')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('allCategories')}</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Enhanced Language Filter */}
                  <div>
                    <Label htmlFor="language-filter" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                      {t('language')}
                    </Label>
                    <Select value={languageFilter} onValueChange={handleLanguageChange}>
                      <SelectTrigger id="language-filter" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                        <SelectValue placeholder={t('allLanguages')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('allLanguages')}</SelectItem>
                        {languages.map((lang) => (
                          <SelectItem key={lang} value={lang}>{lang}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Enhanced Author Filter */}
                  {authors.length > 0 && (
                    <div>
                      <Label htmlFor="author-filter" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                        {t('author')}
                      </Label>
                      <Select value={authorFilter} onValueChange={handleAuthorChange}>
                        <SelectTrigger id="author-filter" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                          <SelectValue placeholder={t('allAuthors')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">{t('allAuthors')}</SelectItem>
                          {authors.map((author) => (
                            <SelectItem key={author} value={author}>{author}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Enhanced Sort By */}
                  <div>
                    <Label htmlFor="sort-by" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                      {t('sortBy')}
                    </Label>
                    <Select value={sortBy} onValueChange={handleSortChange}>
                      <SelectTrigger id="sort-by" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                        <SelectValue placeholder={t('default')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">{t('default')}</SelectItem>
                        <SelectItem value="newest">{language === 'en' ? "Newest" : language === 'fr' ? "Plus récent" : "الأحدث"}</SelectItem>
                        <SelectItem value="popular">{language === 'en' ? "Most Popular" : language === 'fr' ? "Plus populaire" : "الأكثر شعبية"}</SelectItem>
                        <SelectItem value="priceAsc">{t('priceLowToHigh')}</SelectItem>
                        <SelectItem value="priceDesc">{t('priceHighToLow')}</SelectItem>
                        <SelectItem value="nameAsc">{t('nameAZ')}</SelectItem>
                        <SelectItem value="nameDesc">{t('nameZA')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Books Grid */}
            <div className="w-full md:w-3/4">
              {/* Mobile Filters Toggle */}
              <div className="md:hidden mb-6">
                <Button
                  variant="outline"
                  className={`w-full ${theme === 'light' ? 'border-[#e7e1c8] text-[#5c5a4e]' : 'border-primary/20 text-white'}`}
                  onClick={() => setShowMobileFilters(!showMobileFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {t('filters')}
                  <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${showMobileFilters ? 'rotate-180' : ''}`} />
                </Button>

                {showMobileFilters && (
                  <div className={`mt-4 p-4 rounded-lg ${theme === 'light' ? 'bg-white' : 'bg-card'} border ${theme === 'light' ? 'border-[#e7e1c8]' : 'border-primary/20'}`}>
                    <div className="space-y-4">
                      {/* Search */}
                      <div>
                        <Label htmlFor="mobile-search" className={`block mb-2 text-sm font-medium ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                          {t('search')}
                        </Label>
                        <div className="relative">
                          <Input
                            id="mobile-search"
                            type="text"
                            value={searchQuery}
                            onChange={handleSearchChange}
                            placeholder={language === 'en' ? "Search books..." :
                                         language === 'fr' ? "Rechercher des livres..." :
                                         "البحث عن الكتب..."}
                            className={`${theme === 'light' ? 'border-[#e7e1c8] bg-[#f3edda]/30' : 'border-primary/20 bg-card'}`}
                          />
                          <Search className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${theme === 'light' ? 'text-[#398564]' : 'text-primary'}`} />
                        </div>
                      </div>

                      {/* Category Filter */}
                      <div>
                        <Label htmlFor="mobile-category-filter" className={`block mb-2 text-sm font-medium ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                          {t('category')}
                        </Label>
                        <Select value={categoryFilter} onValueChange={handleCategoryChange}>
                          <SelectTrigger id="mobile-category-filter" className={`${theme === 'light' ? 'border-[#e7e1c8] bg-[#f3edda]/50' : 'border-primary/20 bg-card'}`}>
                            <SelectValue placeholder={t('allCategories')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">{t('allCategories')}</SelectItem>
                            {categories.map((category) => (
                              <SelectItem key={category} value={category}>{category}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Sort By */}
                      <div>
                        <Label htmlFor="mobile-sort-by" className={`block mb-2 text-sm font-medium ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                          {t('sortBy')}
                        </Label>
                        <Select value={sortBy} onValueChange={handleSortChange}>
                          <SelectTrigger id="mobile-sort-by" className={`${theme === 'light' ? 'border-[#e7e1c8] bg-[#f3edda]/50' : 'border-primary/20 bg-card'}`}>
                            <SelectValue placeholder={t('default')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">{t('default')}</SelectItem>
                            <SelectItem value="newest">{language === 'en' ? "Newest" : language === 'fr' ? "Plus récent" : "الأحدث"}</SelectItem>
                            <SelectItem value="popular">{language === 'en' ? "Most Popular" : language === 'fr' ? "Plus populaire" : "الأكثر شعبية"}</SelectItem>
                            <SelectItem value="priceAsc">{t('priceLowToHigh')}</SelectItem>
                            <SelectItem value="priceDesc">{t('priceHighToLow')}</SelectItem>
                            <SelectItem value="nameAsc">{t('nameAZ')}</SelectItem>
                            <SelectItem value="nameDesc">{t('nameZA')}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex justify-between pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className={`${theme === 'light' ? 'border-[#e7e1c8] text-[#5c5a4e]' : 'border-primary/20 text-white'}`}
                          onClick={clearFilters}
                        >
                          {t('clearAll')}
                        </Button>
                        <Button
                          size="sm"
                          className={`${theme === 'light' ? 'bg-[#398564] text-white' : 'bg-primary text-white'}`}
                          onClick={() => setShowMobileFilters(false)}
                        >
                          {language === 'en' ? "Apply" : language === 'fr' ? "Appliquer" : "تطبيق"}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Enhanced Results Count */}
              <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="flex items-center gap-3">
                  <div className={`px-4 py-2 rounded-lg ${theme === 'light' ? 'bg-[#6e191c]/10 border border-[#6e191c]/20' : 'bg-[#6e191c]/20 border border-[#6e191c]/30'}`}>
                    <span className={`text-xl font-bold ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}`}>
                      {filteredBooks.length}
                    </span>
                  </div>
                  <div>
                    <h2 className={`text-lg font-bold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}>
                      {language === 'en' ? "Books Found" : language === 'fr' ? "Livres Trouvés" : "كتب موجودة"}
                    </h2>
                    {filteredBooks.length > itemsPerPage && (
                      <span className={`text-sm font-medium ${theme === 'light' ? 'text-[#6e191c]/70' : 'text-white/70'}`}>
                        {language === 'en'
                          ? `Page ${currentPage} of ${Math.ceil(filteredBooks.length / itemsPerPage)}`
                          : language === 'fr'
                          ? `Page ${currentPage} sur ${Math.ceil(filteredBooks.length / itemsPerPage)}`
                          : `الصفحة ${currentPage} من ${Math.ceil(filteredBooks.length / itemsPerPage)}`}
                      </span>
                    )}
                  </div>
                </div>

                {searchQuery && (
                  <div className={`px-4 py-2 rounded-lg ${theme === 'light' ? 'bg-[#6e191c]/5 border border-[#6e191c]/15' : 'bg-[#6e191c]/15 border border-[#6e191c]/25'}`}>
                    <span className={`text-sm font-medium ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}>
                      {language === 'en' ? `"${searchQuery}"` :
                       language === 'fr' ? `"${searchQuery}"` :
                       `"${searchQuery}"`}
                    </span>
                  </div>
                )}
              </div>

              {/* Enhanced Books Grid */}
              {isLoading ? (
                // Enhanced loading state with skeleton cards
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                  {Array(6).fill(0).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className={`${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-[#6e191c]/20'} rounded-2xl shadow-lg h-80 mb-6`}></div>
                      <div className={`h-5 ${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-[#6e191c]/20'} rounded-lg w-3/4 mb-3`}></div>
                      <div className={`h-4 ${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-[#6e191c]/20'} rounded-lg w-1/2 mb-3`}></div>
                      <div className={`h-5 ${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-[#6e191c]/20'} rounded-lg w-1/4`}></div>
                    </div>
                  ))}
                </div>
              ) : filteredBooks.length > 0 ? (
                <div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    {filteredBooks
                      .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                      .map(book => book && <BookCard key={book.id} book={book} />)}
                  </div>

                  {/* Enhanced Pagination */}
                  {filteredBooks.length > itemsPerPage && (
                    <div className="flex justify-center mt-16">
                      <div className={`flex items-center gap-3 p-2 rounded-2xl ${theme === 'light' ? 'bg-white shadow-[0_8px_30px_rgba(110,25,28,0.08)] border border-[#6e191c]/10' : 'bg-[#2a2a2a] shadow-[0_8px_30px_rgba(110,25,28,0.15)] border border-[#6e191c]/20'}`}>
                        <Button
                          variant="outline"
                          size="sm"
                          className={`transition-all duration-300 hover:scale-105 ${theme === 'light' ? 'border-[#6e191c]/20 hover:bg-[#6e191c]/10 hover:border-[#6e191c]/30' : 'border-[#6e191c]/25 hover:bg-[#6e191c]/20 hover:border-[#6e191c]/40'}`}
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                        >
                          {language === 'en' ? "Previous" : language === 'fr' ? "Précédent" : "السابق"}
                        </Button>

                        {/* Enhanced Page Numbers */}
                        {Array.from({ length: Math.ceil(filteredBooks.length / itemsPerPage) }, (_, i) => i + 1)
                          .map(pageNum => (
                            <Button
                              key={pageNum}
                              variant="outline"
                              size="sm"
                              className={`transition-all duration-300 hover:scale-105 ${pageNum === currentPage
                                ? (theme === 'light'
                                  ? 'bg-[#6e191c] border-[#6e191c] text-white shadow-[0_4px_15px_rgba(110,25,28,0.3)]'
                                  : 'bg-[#6e191c] border-[#6e191c] text-white shadow-[0_4px_15px_rgba(110,25,28,0.4)]')
                                : (theme === 'light'
                                  ? 'border-[#6e191c]/20 hover:bg-[#6e191c]/10 hover:border-[#6e191c]/30'
                                  : 'border-[#6e191c]/25 hover:bg-[#6e191c]/20 hover:border-[#6e191c]/40')}`}
                              onClick={() => setCurrentPage(pageNum)}
                            >
                              {pageNum}
                            </Button>
                          ))
                        }

                        <Button
                          variant="outline"
                          size="sm"
                          className={`transition-all duration-300 hover:scale-105 ${theme === 'light' ? 'border-[#6e191c]/20 hover:bg-[#6e191c]/10 hover:border-[#6e191c]/30' : 'border-[#6e191c]/25 hover:bg-[#6e191c]/20 hover:border-[#6e191c]/40'}`}
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(filteredBooks.length / itemsPerPage)))}
                          disabled={currentPage === Math.ceil(filteredBooks.length / itemsPerPage)}
                        >
                          {language === 'en' ? "Next" : language === 'fr' ? "Suivant" : "التالي"}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className={`${theme === 'light' ? 'bg-white shadow-[0_8px_30px_rgba(110,25,28,0.08)]' : 'bg-[#2a2a2a] shadow-[0_8px_30px_rgba(110,25,28,0.15)]'} p-12 rounded-2xl text-center border ${theme === 'light' ? 'border-[#6e191c]/10' : 'border-[#6e191c]/20'} transition-all duration-300 hover:shadow-xl`}>
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center ${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-[#6e191c]/20'}`}>
                    <BookText className={`h-10 w-10 ${theme === 'light' ? 'text-[#6e191c]/60' : 'text-[#6e191c]/80'}`} />
                  </div>
                  <h3 className={`text-2xl font-bold mb-3 ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}>
                    {t('noBooks')}
                  </h3>
                  <p className={`text-base mb-8 max-w-md mx-auto leading-relaxed ${theme === 'light' ? 'text-[#6e191c]/70' : 'text-white/70'}`}>
                    {t('tryDifferentFilters')}
                  </p>
                  <Button
                    variant="outline"
                    className={`px-8 py-3 font-semibold transition-all duration-300 hover:scale-105 ${theme === 'light' ? 'border-[#6e191c]/20 text-[#6e191c] hover:bg-[#6e191c]/10 hover:border-[#6e191c]/30' : 'border-[#6e191c]/25 text-[#6e191c] hover:bg-[#6e191c]/20 hover:border-[#6e191c]/40'}`}
                    onClick={clearFilters}
                  >
                    {t('clearFilters')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default BooksPage;
