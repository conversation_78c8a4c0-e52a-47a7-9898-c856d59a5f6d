import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import BookCard from '@/components/BookCard';
import { BookData } from '@/data/books';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { fetchQuranBooks } from '@/services/bookService';
import {
  Search,
  Filter,
  ChevronDown,
  BookText
} from 'lucide-react';
const QuranPage = () => {
  const { t, language } = useLanguage();
  const { theme } = useTheme();
  const isRTL = language === 'ar';

  // State for Quran books and filters
  const [allQuranBooks, setAllQuranBooks] = useState<BookData[]>([]);
  const [filteredQuran, setFilteredQuran] = useState<BookData[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [languageFilter, setLanguageFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [priceRangeFilter, setPriceRangeFilter] = useState<[number, number]>([0, 1000]);
  const [sortBy, setSortBy] = useState<string>('default');
  const [isLoading, setIsLoading] = useState(true);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Load Quran books
  useEffect(() => {
    const loadQuranBooks = async () => {
      try {
        setIsLoading(true);
        const quranBooks = await fetchQuranBooks();
        setAllQuranBooks(quranBooks);
        setFilteredQuran(quranBooks);
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading quran books:', error);
        setIsLoading(false);
      }
    };
    loadQuranBooks();
  }, []);

  // Get unique languages for Quran with null check
  const languages = [...new Set(allQuranBooks
    .filter(book => book && book.language) // Filter out null/undefined values
    .map(book => book.language))];

  // Get unique types for Quran
  const types = [...new Set(allQuranBooks
    .filter(book => book && book.category)
    .map(book => book.category))];

  // Get min and max prices
  const prices = allQuranBooks
    .filter(book => book && typeof book.price === 'number')
    .map(book => book.price as number);
  const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
  const maxPrice = prices.length > 0 ? Math.max(...prices) : 1000;



  // Handle filter changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    applyFilters(value, languageFilter, typeFilter, priceRangeFilter, sortBy);
  };

  const handleLanguageChange = (value: string) => {
    setLanguageFilter(value);
    applyFilters(searchQuery, value, typeFilter, priceRangeFilter, sortBy);
  };

  const handleTypeChange = (value: string) => {
    setTypeFilter(value);
    applyFilters(searchQuery, languageFilter, value, priceRangeFilter, sortBy);
  };

  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRangeFilter(range);
    applyFilters(searchQuery, languageFilter, typeFilter, range, sortBy);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    applyFilters(searchQuery, languageFilter, typeFilter, priceRangeFilter, value);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setLanguageFilter('all');
    setTypeFilter('all');
    setPriceRangeFilter([minPrice, maxPrice]);
    setSortBy('default');
    setFilteredQuran(allQuranBooks);
    setShowMobileFilters(false);
  };

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, languageFilter, typeFilter, priceRangeFilter, sortBy]);

  // Apply all filters
  const applyFilters = (
    search: string,
    lang: string,
    type: string,
    priceRange: [number, number],
    sort: string
  ) => {
    if (!allQuranBooks || allQuranBooks.length === 0) {
      setFilteredQuran([]);
      return;
    }

    let result = [...allQuranBooks];

    // Apply search filter
    if (search.trim() !== '') {
      const searchLower = search.toLowerCase();
      result = result.filter(book =>
        book &&
        (
          (book.title && book.title.toLowerCase().includes(searchLower)) ||
          (book.author && book.author.toLowerCase().includes(searchLower)) ||
          (book.description && book.description.toLowerCase().includes(searchLower))
        )
      );
    }

    // Apply language filter
    if (lang !== 'all') {
      result = result.filter(book => book && book.language === lang);
    }

    // Apply type filter
    if (type !== 'all') {
      result = result.filter(book => book && book.category === type);
    }

    // Apply price range filter
    result = result.filter(book =>
      book &&
      typeof book.price === 'number' &&
      book.price >= priceRange[0] &&
      book.price <= priceRange[1]
    );

    // Apply sorting
    switch (sort) {
      case 'priceAsc':
        result.sort((a, b) => (a.price || 0) - (b.price || 0));
        break;
      case 'priceDesc':
        result.sort((a, b) => (b.price || 0) - (a.price || 0));
        break;
      case 'nameAsc':
        result.sort((a, b) => (a.title || '').localeCompare(b.title || ''));
        break;
      case 'nameDesc':
        result.sort((a, b) => (b.title || '').localeCompare(a.title || ''));
        break;
      case 'newest':
        // If you want to sort by publisher alphabetically (since publishDate does not exist)
        result.sort((a, b) => (b.publisher || '').localeCompare(a.publisher || ''));
        break;
      case 'popular':
        result.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      default:
        // Default sorting - keep as is
        break;
    }

    setFilteredQuran(result);
  };
  return (
    <main className={`${theme === 'light' ? 'bg-white' : 'bg-gradient-to-b from-[#050505] via-[#0a0a0a] to-[#050505]'} transition-all duration-500`}>
      {/* 🌙 ULTRA PREMIUM QURAN HERO SECTION */}
      <section className="relative overflow-hidden">
        {/* 💫 Majestic Background System */}
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1609599006353-e629aaabfeae?q=80&w=2000"
            alt="Quran Background"
            className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
          />
          {/* 🎭 Sacred Overlay System */}
          <div className={`absolute inset-0 ${theme === 'light' ? 'bg-gradient-to-br from-[#6e191c]/85 via-[#8a1e24]/90 to-[#6e191c]/85' : 'bg-gradient-to-br from-[#050505]/96 via-[#0a0a0a]/92 to-[#050505]/96'}`}></div>
          <div className={`absolute inset-0 ${theme === 'light' ? 'bg-gradient-to-b from-[#6e191c]/80 via-transparent to-white/20' : 'bg-gradient-to-b from-[#050505]/92 via-[#0a0a0a]/70 to-[#050505]/92'}`}></div>

          {/* 🌟 Divine Decorative Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className={`absolute top-0 right-0 w-[700px] h-[700px] ${theme === 'light' ? 'bg-white/10' : 'bg-gradient-radial from-[#6e191c]/30 via-[#6e191c]/20 to-transparent'} rounded-full blur-3xl animate-pulse-slow`}></div>
            <div className={`absolute bottom-0 left-0 w-[600px] h-[600px] ${theme === 'light' ? 'bg-white/8' : 'bg-gradient-radial from-[#8a1e24]/25 via-[#6e191c]/15 to-transparent'} rounded-full blur-3xl animate-floating`}></div>
            <div className={`absolute top-1/3 left-1/3 w-[500px] h-[500px] ${theme === 'light' ? 'bg-white/5' : 'bg-gradient-radial from-[#6e191c]/20 to-transparent'} rounded-full blur-3xl`}></div>

            {/* 🕌 Islamic Geometric Patterns */}
            <div className={`absolute top-24 right-24 w-48 h-48 border ${theme === 'light' ? 'border-white/20' : 'border-[#6e191c]/35'} rounded-full opacity-60 animate-spin-slow`}>
              <div className={`absolute inset-6 border ${theme === 'light' ? 'border-white/15' : 'border-[#6e191c]/30'} rounded-full`}>
                <div className={`absolute inset-6 border ${theme === 'light' ? 'border-white/10' : 'border-[#6e191c]/25'} rounded-full`}>
                  <div className={`absolute inset-6 ${theme === 'light' ? 'bg-white/5' : 'bg-gradient-to-br from-[#6e191c]/20 to-transparent'} rounded-full`}></div>
                </div>
              </div>
            </div>
            <div className={`absolute bottom-32 left-20 w-36 h-36 border ${theme === 'light' ? 'border-white/15' : 'border-[#6e191c]/30'} rounded-full opacity-50`}>
              <div className={`absolute inset-3 ${theme === 'light' ? 'bg-white/5' : 'bg-gradient-to-br from-[#6e191c]/18 to-transparent'} rounded-full blur-sm`}></div>
            </div>

            {/* ✨ Sacred Light Particles */}
            {theme === 'dark' && (
              <>
                <div className="absolute top-20 left-1/4 w-4 h-4 bg-[#6e191c] rounded-full opacity-80 animate-pulse shadow-lg shadow-[#6e191c]/60"></div>
                <div className="absolute bottom-40 right-1/3 w-3 h-3 bg-[#8a1e24] rounded-full opacity-85 animate-pulse shadow-lg shadow-[#8a1e24]/60" style={{animationDelay: '1.5s'}}></div>
                <div className="absolute top-2/3 left-1/3 w-3.5 h-3.5 bg-[#6e191c] rounded-full opacity-90 animate-pulse shadow-lg shadow-[#6e191c]/60" style={{animationDelay: '2.5s'}}></div>
                <div className="absolute top-1/4 right-1/5 w-2.5 h-2.5 bg-[#a52229] rounded-full opacity-75 animate-pulse shadow-lg shadow-[#a52229]/60" style={{animationDelay: '3s'}}></div>
              </>
            )}
          </div>

          {/* 🔥 Sacred Gradient Borders */}
          <div className={`absolute top-0 left-0 w-full h-[4px] ${theme === 'light' ? 'bg-gradient-to-r from-transparent via-white/30 to-transparent' : 'bg-gradient-to-r from-transparent via-[#6e191c]/70 to-transparent'}`}></div>
          <div className={`absolute bottom-0 left-0 w-full h-[4px] ${theme === 'light' ? 'bg-gradient-to-r from-transparent via-white/30 to-transparent' : 'bg-gradient-to-r from-transparent via-[#6e191c]/70 to-transparent'}`}></div>
        </div>

        {/* Enhanced Content */}
        <div className="container relative z-10 pt-20 pb-24 md:pt-28 md:pb-32">
          <div className={`max-w-4xl mx-auto text-center ${isRTL ? 'rtl' : ''}`}>
            <Badge className={`mb-6 px-5 py-2 text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg ${theme === 'light' ? 'bg-white/95 text-[#6e191c] shadow-[0_4px_15px_rgba(255,255,255,0.2)] backdrop-blur-sm' : 'bg-[#6e191c]/90 text-white shadow-[0_4px_15px_rgba(110,25,28,0.3)] backdrop-blur-sm'}`}>
              {language === 'en' ? "Holy Quran Collection" :
               language === 'fr' ? "Collection du Saint Coran" :
               "مجموعة القرآن الكريم"}
            </Badge>

            <h1 className="text-4xl md:text-5xl lg:text-7xl font-serif font-bold mb-8 text-white leading-tight tracking-tight">
              {language === 'en' ? "Explore the Holy Quran" :
               language === 'fr' ? "Explorez le Saint Coran" :
               "استكشف القرآن الكريم"}
            </h1>

            {/* Enhanced decorative underline */}
            <div className={`h-1.5 w-32 mx-auto rounded-full mb-8 ${theme === 'light' ? 'bg-gradient-to-r from-white/40 to-white/20' : 'bg-gradient-to-r from-[#6e191c]/60 to-[#6e191c]/30'}`}></div>

            <p className="text-xl md:text-2xl text-white/95 mb-12 max-w-3xl mx-auto leading-relaxed font-medium">
              {language === 'en' ? "Discover our collection of beautifully crafted Quran editions in various languages, styles, and formats." :
               language === 'fr' ? "Découvrez notre collection d'éditions du Coran magnifiquement conçues en différentes langues, styles et formats." :
               "اكتشف مجموعتنا من إصدارات القرآن المصممة بشكل جميل بلغات وأساليب وتنسيقات مختلفة."}
            </p>

            {/* Enhanced Search Bar */}
            <div className={`max-w-2xl mx-auto relative group`}>
              <div className={`${theme === 'light' ? 'bg-white/95 shadow-[0_8px_30px_rgba(255,255,255,0.2)]' : 'bg-[#2a2a2a]/95 shadow-[0_8px_30px_rgba(110,25,28,0.2)]'} rounded-2xl p-2 backdrop-blur-md transition-all duration-300 group-hover:shadow-xl group-hover:scale-[1.02]`}>
                <form onSubmit={(e) => {
                  e.preventDefault();

                  // Apply filters first
                  applyFilters(searchQuery, languageFilter, typeFilter, priceRangeFilter, sortBy);

                  // Check if there's only one result after filtering
                  const results = allQuranBooks.filter(book =>
                    book &&
                    (book.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     book.author?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     book.description?.toLowerCase().includes(searchQuery.toLowerCase()))
                  );

                  // If there's exactly one result, redirect to that Quran's detail page
                  if (results.length === 1 && searchQuery.trim() !== '') {
                    window.location.href = `/quran/${results[0].id}`;
                  }
                }} className="flex items-center">
                  <div className="relative flex-grow">
                    <Search className={`absolute left-5 top-1/2 transform -translate-y-1/2 h-5 w-5 ${theme === 'light' ? 'text-[#6e191c]/60' : 'text-white/60'} transition-all duration-300 group-hover:scale-110`} />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      placeholder={language === 'en' ? "Search Quran editions, translations..." :
                                   language === 'fr' ? "Rechercher des éditions du Coran, traductions..." :
                                   "البحث عن إصدارات القرآن والترجمات..."}
                      className={`w-full pl-14 pr-6 py-4 bg-transparent border-none focus:outline-none text-base font-medium ${theme === 'light' ? 'text-[#6e191c] placeholder:text-[#6e191c]/60' : 'text-white placeholder:text-white/60'}`}
                    />
                  </div>
                  <button
                    type="submit"
                    className={`px-8 py-4 rounded-xl font-semibold text-base transition-all duration-300 relative overflow-hidden group/btn ${theme === 'light' ? 'bg-[#6e191c] hover:bg-[#8a1e24] text-white shadow-[0_4px_15px_rgba(110,25,28,0.3)]' : 'bg-[#6e191c] hover:bg-[#8a1e24] text-white shadow-[0_4px_15px_rgba(110,25,28,0.4)]'} hover:scale-105 hover:shadow-xl`}
                  >
                    {/* Shine effect */}
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/15 to-transparent translate-x-[-100%] group-hover/btn:translate-x-[100%] transition-transform duration-1000"></div>

                    <span className="relative z-10 tracking-wide">{t('search')}</span>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Enhanced Main Quran Catalog */}
      <section className={`py-20 ${theme === 'light' ? 'bg-gradient-to-b from-white to-[#fafafa]' : 'bg-gradient-to-b from-[#1a1a1a] to-[#1f1f1f]'}`}>
        <div className="container">
          <div className="mb-12">
            <Badge className={`mb-4 px-4 py-2 text-sm font-semibold transition-all duration-300 hover:scale-105 ${theme === 'light' ? 'bg-[#6e191c]/10 text-[#6e191c] border border-[#6e191c]/20 shadow-[0_4px_15px_rgba(110,25,28,0.1)]' : 'bg-[#6e191c]/20 text-[#6e191c] border border-[#6e191c]/30 shadow-[0_4px_15px_rgba(110,25,28,0.2)]'}`}>
              {language === 'en' ? "Browse" : language === 'fr' ? "Parcourir" : "تصفح"}
            </Badge>
            <h2 className={`text-3xl md:text-4xl font-serif font-bold tracking-tight ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}>
              {language === 'en' ? "Explore Our Quran Collection" :
               language === 'fr' ? "Explorez notre collection du Coran" :
               "استكشف مجموعة القرآن الكريم"}
            </h2>
            {/* Enhanced decorative underline */}
            <div className={`h-1 w-24 rounded-full mt-4 ${theme === 'light' ? 'bg-gradient-to-r from-[#6e191c]/30 to-[#6e191c]/10' : 'bg-gradient-to-r from-[#6e191c]/50 to-[#6e191c]/20'}`}></div>
          </div>

          <div className={`flex flex-col md:flex-row gap-8 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            {/* Enhanced Filters Sidebar */}
            <div className="w-full md:w-1/4">
              <div className={`${theme === 'light' ? 'bg-white shadow-[0_8px_30px_rgba(110,25,28,0.08)]' : 'bg-[#2a2a2a] shadow-[0_8px_30px_rgba(110,25,28,0.15)]'} p-8 rounded-2xl sticky top-6 border ${theme === 'light' ? 'border-[#6e191c]/10' : 'border-[#6e191c]/20'} transition-all duration-300 hover:shadow-xl`}>
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${theme === 'light' ? 'bg-[#6e191c]/10' : 'bg-[#6e191c]/20'} transition-all duration-300 hover:scale-110`}>
                      <Filter className={`h-5 w-5 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'}`} />
                    </div>
                    <h2 className={`text-xl font-bold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'}`}>
                      {t('filters')}
                    </h2>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`text-sm font-medium transition-all duration-300 hover:scale-105 ${theme === 'light' ? 'text-[#6e191c] hover:bg-[#6e191c]/10' : 'text-[#6e191c] hover:bg-[#6e191c]/20'}`}
                    onClick={clearFilters}
                  >
                    {t('clearAll')}
                  </Button>
                </div>

                <div className="space-y-8">
                  {/* Enhanced Language Filter */}
                  <div>
                    <Label htmlFor="language-filter" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                      {t('language')}
                    </Label>
                    <Select value={languageFilter} onValueChange={handleLanguageChange}>
                      <SelectTrigger id="language-filter" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                        <SelectValue placeholder={t('allLanguages')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('allLanguages')}</SelectItem>
                        {languages.map(lang => (
                          <SelectItem key={lang} value={lang}>{lang}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Enhanced Type Filter */}
                  {types.length > 0 && (
                    <div>
                      <Label htmlFor="type-filter" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                        {language === 'en' ? "Type" : language === 'fr' ? "Type" : "النوع"}
                      </Label>
                      <Select value={typeFilter} onValueChange={handleTypeChange}>
                        <SelectTrigger id="type-filter" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                          <SelectValue placeholder={language === 'en' ? "All Types" : language === 'fr' ? "Tous les types" : "جميع الأنواع"} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">{language === 'en' ? "All Types" : language === 'fr' ? "Tous les types" : "جميع الأنواع"}</SelectItem>
                          {types.map(type => (
                            <SelectItem key={type} value={type}>{type}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Enhanced Sort By */}
                  <div>
                    <Label htmlFor="sort-by" className={`block mb-3 text-sm font-semibold ${theme === 'light' ? 'text-[#6e191c]' : 'text-white'} ${isRTL ? 'text-right' : ''}`}>
                      {t('sortBy')}
                    </Label>
                    <Select value={sortBy} onValueChange={handleSortChange}>
                      <SelectTrigger id="sort-by" className={`h-12 transition-all duration-300 hover:scale-[1.02] ${theme === 'light' ? 'border-[#6e191c]/20 bg-[#6e191c]/5 hover:border-[#6e191c]/30 focus:border-[#6e191c]/50' : 'border-[#6e191c]/25 bg-[#6e191c]/10 hover:border-[#6e191c]/40 focus:border-[#6e191c]/60'}`}>
                        <SelectValue placeholder={t('default')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">{t('default')}</SelectItem>
                        <SelectItem value="newest">{language === 'en' ? "Newest" : language === 'fr' ? "Plus récent" : "الأحدث"}</SelectItem>
                        <SelectItem value="popular">{language === 'en' ? "Most Popular" : language === 'fr' ? "Plus populaire" : "الأكثر شعبية"}</SelectItem>
                        <SelectItem value="priceAsc">{t('priceLowToHigh')}</SelectItem>
                        <SelectItem value="priceDesc">{t('priceHighToLow')}</SelectItem>
                        <SelectItem value="nameAsc">{t('nameAZ')}</SelectItem>
                        <SelectItem value="nameDesc">{t('nameZA')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Quran Books Grid */}
            <div className="w-full md:w-3/4">
              {/* Mobile Filters Toggle */}
              <div className="md:hidden mb-6">
                <Button
                  variant="outline"
                  className={`w-full ${theme === 'light' ? 'border-[#e7e1c8] text-[#5c5a4e]' : 'border-primary/20 text-white'}`}
                  onClick={() => setShowMobileFilters(!showMobileFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {t('filters')}
                  <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${showMobileFilters ? 'rotate-180' : ''}`} />
                </Button>

                {showMobileFilters && (
                  <div className={`mt-4 p-4 rounded-lg ${theme === 'light' ? 'bg-white' : 'bg-card'} border ${theme === 'light' ? 'border-[#e7e1c8]' : 'border-primary/20'}`}>
                    <div className="space-y-4">
                      {/* Search */}
                      <div>
                        <Label htmlFor="mobile-search" className={`block mb-2 text-sm font-medium ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                          {t('search')}
                        </Label>
                        <div className="relative">
                          <Input
                            id="mobile-search"
                            type="text"
                            value={searchQuery}
                            onChange={handleSearchChange}
                            placeholder={language === 'en' ? "Search Quran editions..." :
                                         language === 'fr' ? "Rechercher des éditions du Coran..." :
                                         "البحث عن إصدارات القرآن..."}
                            className={`${theme === 'light' ? 'border-[#e7e1c8] bg-[#f3edda]/30' : 'border-primary/20 bg-card'}`}
                          />
                          <Search className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${theme === 'light' ? 'text-[#398564]' : 'text-primary'}`} />
                        </div>
                      </div>

                      {/* Language Filter */}
                      <div>
                        <Label htmlFor="mobile-language-filter" className={`block mb-2 text-sm font-medium ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                          {t('language')}
                        </Label>
                        <Select value={languageFilter} onValueChange={handleLanguageChange}>
                          <SelectTrigger id="mobile-language-filter" className={`${theme === 'light' ? 'border-[#e7e1c8] bg-[#f3edda]/50' : 'border-primary/20 bg-card'}`}>
                            <SelectValue placeholder={t('allLanguages')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">{t('allLanguages')}</SelectItem>
                            {languages.map(lang => (
                              <SelectItem key={lang} value={lang}>{lang}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Sort By */}
                      <div>
                        <Label htmlFor="mobile-sort-by" className={`block mb-2 text-sm font-medium ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                          {t('sortBy')}
                        </Label>
                        <Select value={sortBy} onValueChange={handleSortChange}>
                          <SelectTrigger id="mobile-sort-by" className={`${theme === 'light' ? 'border-[#e7e1c8] bg-[#f3edda]/50' : 'border-primary/20 bg-card'}`}>
                            <SelectValue placeholder={t('default')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">{t('default')}</SelectItem>
                            <SelectItem value="newest">{language === 'en' ? "Newest" : language === 'fr' ? "Plus récent" : "الأحدث"}</SelectItem>
                            <SelectItem value="popular">{language === 'en' ? "Most Popular" : language === 'fr' ? "Plus populaire" : "الأكثر شعبية"}</SelectItem>
                            <SelectItem value="priceAsc">{t('priceLowToHigh')}</SelectItem>
                            <SelectItem value="priceDesc">{t('priceHighToLow')}</SelectItem>
                            <SelectItem value="nameAsc">{t('nameAZ')}</SelectItem>
                            <SelectItem value="nameDesc">{t('nameZA')}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex justify-between pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className={`${theme === 'light' ? 'border-[#e7e1c8] text-[#5c5a4e]' : 'border-primary/20 text-white'}`}
                          onClick={clearFilters}
                        >
                          {t('clearAll')}
                        </Button>
                        <Button
                          size="sm"
                          className={`${theme === 'light' ? 'bg-[#398564] text-white' : 'bg-primary text-white'}`}
                          onClick={() => setShowMobileFilters(false)}
                        >
                          {language === 'en' ? "Apply" : language === 'fr' ? "Appliquer" : "تطبيق"}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Results Count */}
              <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <h2 className={`text-lg font-bold ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                  {filteredQuran.length} {language === 'en' ? "Quran Editions" : language === 'fr' ? "Éditions du Coran" : "إصدارات القرآن"}
                  {filteredQuran.length > itemsPerPage && (
                    <span className={`ml-2 text-sm font-normal ${theme === 'light' ? 'text-[#5c5a4e]/70' : 'text-white/70'}`}>
                      {language === 'en'
                        ? `(Page ${currentPage} of ${Math.ceil(filteredQuran.length / itemsPerPage)})`
                        : language === 'fr'
                        ? `(Page ${currentPage} sur ${Math.ceil(filteredQuran.length / itemsPerPage)})`
                        : `(الصفحة ${currentPage} من ${Math.ceil(filteredQuran.length / itemsPerPage)})`}
                    </span>
                  )}
                </h2>

                {searchQuery && (
                  <div className={`text-sm ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white/70'}`}>
                    {language === 'en' ? `Search results for "${searchQuery}"` :
                     language === 'fr' ? `Résultats de recherche pour "${searchQuery}"` :
                     `نتائج البحث عن "${searchQuery}"`}
                  </div>
                )}
              </div>

              {/* Quran Books Grid */}
              {isLoading ? (
                // Loading state with skeleton cards
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array(6).fill(0).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className={`${theme === 'light' ? 'bg-[#e7e1c8]/50' : 'bg-card/50'} rounded-lg shadow-sm h-64 mb-4`}></div>
                      <div className={`h-4 ${theme === 'light' ? 'bg-[#e7e1c8]/50' : 'bg-card/50'} rounded w-3/4 mb-2`}></div>
                      <div className={`h-3 ${theme === 'light' ? 'bg-[#e7e1c8]/50' : 'bg-card/50'} rounded w-1/2 mb-2`}></div>
                      <div className={`h-4 ${theme === 'light' ? 'bg-[#e7e1c8]/50' : 'bg-card/50'} rounded w-1/4`}></div>
                    </div>
                  ))}
                </div>
              ) : filteredQuran.length > 0 ? (
                <div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredQuran
                      .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                      .map(book => book && <BookCard key={book.id} book={book} />)}
                  </div>

                  {/* Pagination */}
                  {filteredQuran.length > itemsPerPage && (
                    <div className="flex justify-center mt-12">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className={`${theme === 'light' ? 'border-[#e7e1c8]' : 'border-primary/20'}`}
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                        >
                          {language === 'en' ? "Previous" : language === 'fr' ? "Précédent" : "السابق"}
                        </Button>

                        {/* Page Numbers */}
                        {Array.from({ length: Math.ceil(filteredQuran.length / itemsPerPage) }, (_, i) => i + 1)
                          .map(pageNum => (
                            <Button
                              key={pageNum}
                              variant="outline"
                              size="sm"
                              className={`${pageNum === currentPage
                                ? (theme === 'light'
                                  ? 'bg-[#f3edda] border-[#e7e1c8] text-[#398564]'
                                  : 'bg-primary/10 border-primary/20 text-primary')
                                : (theme === 'light'
                                  ? 'border-[#e7e1c8]'
                                  : 'border-primary/20')}`}
                              onClick={() => setCurrentPage(pageNum)}
                            >
                              {pageNum}
                            </Button>
                          ))
                        }

                        <Button
                          variant="outline"
                          size="sm"
                          className={`${theme === 'light' ? 'border-[#e7e1c8]' : 'border-primary/20'}`}
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(filteredQuran.length / itemsPerPage)))}
                          disabled={currentPage === Math.ceil(filteredQuran.length / itemsPerPage)}
                        >
                          {language === 'en' ? "Next" : language === 'fr' ? "Suivant" : "التالي"}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className={`${theme === 'light' ? 'bg-white' : 'bg-card'} p-8 rounded-lg text-center border ${theme === 'light' ? 'border-[#e7e1c8]' : 'border-primary/20'}`}>
                  <BookText className={`h-12 w-12 mx-auto mb-4 ${theme === 'light' ? 'text-[#398564]/50' : 'text-primary/50'}`} />
                  <p className={`text-lg font-medium mb-2 ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
                    {t('noQuranFound')}
                  </p>
                  <p className={`text-sm ${theme === 'light' ? 'text-[#5c5a4e]/70' : 'text-white/70'} mb-4`}>
                    {t('tryDifferentFilters')}
                  </p>
                  <Button
                    variant="outline"
                    className={`${theme === 'light' ? 'border-[#e7e1c8] text-[#398564]' : 'border-primary/20 text-primary'}`}
                    onClick={clearFilters}
                  >
                    {t('clearFilters')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Information Section */}
      <section className={`py-16 ${theme === 'light' ? 'bg-[#f3edda]/50' : 'bg-card/5'}`}>
        <div className="container">
          <div className={`max-w-3xl mx-auto text-center ${isRTL ? 'rtl' : ''}`}>
            <Badge className={`mb-4 ${theme === 'light' ? 'bg-[#e7e1c8] text-[#398564]' : 'bg-primary/20 text-primary'}`}>
              {language === 'en' ? "About Our Collection" :
               language === 'fr' ? "À propos de notre collection" :
               "حول مجموعتنا"}
            </Badge>

            <h2 className={`text-2xl md:text-3xl font-serif font-bold mb-6 ${theme === 'light' ? 'text-[#5c5a4e]' : 'text-white'}`}>
              {language === 'en' ? "The Holy Quran: Guidance for Humanity" :
               language === 'fr' ? "Le Saint Coran : Guide pour l'humanité" :
               "القرآن الكريم: هداية للبشرية"}
            </h2>

            <p className={`mb-6 ${theme === 'light' ? 'text-[#5c5a4e]/90' : 'text-white/80'}`}>
              {language === 'en' ?
                "Our collection features a wide range of Quran editions, including different translations, tafsirs (interpretations), and beautiful print formats. We carefully select each edition to ensure quality and authenticity." :
               language === 'fr' ?
                "Notre collection comprend une large gamme d'éditions du Coran, y compris différentes traductions, tafsirs (interprétations) et de beaux formats d'impression. Nous sélectionnons soigneusement chaque édition pour garantir la qualité et l'authenticité." :
                "تضم مجموعتنا مجموعة واسعة من إصدارات القرآن، بما في ذلك الترجمات المختلفة والتفاسير وتنسيقات الطباعة الجميلة. نحن نختار بعناية كل إصدار لضمان الجودة والأصالة."}
            </p>

            <p className={`${theme === 'light' ? 'text-[#5c5a4e]/90' : 'text-white/80'}`}>
              {language === 'en' ?
                "Whether you're looking for a study Quran with detailed commentary, a beautifully illustrated edition for gifts, or a simple translation for daily reading, you'll find it in our collection." :
               language === 'fr' ?
                "Que vous recherchiez un Coran d'étude avec des commentaires détaillés, une édition magnifiquement illustrée pour des cadeaux ou une simple traduction pour la lecture quotidienne, vous le trouverez dans notre collection." :
                "سواء كنت تبحث عن قرآن للدراسة مع تعليقات مفصلة، أو إصدار مزين بالرسوم التوضيحية للهدايا، أو ترجمة بسيطة للقراءة اليومية، ستجده في مجموعتنا."}
            </p>
          </div>
        </div>
      </section>
    </main>
  );
};
export default QuranPage;