import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { ChevronRight, ShoppingCart, BookOpen } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useEffect, useState } from 'react';

const Hero = () => {
  const { t, language } = useLanguage();
  const isRTL = language === 'ar';
  const [isLoaded, setIsLoaded] = useState(false);

  // Hero content
  const heroContent = {
    tag: language === 'en' ? "Islamic Literature" : language === 'fr' ? "Littérature Islamique" : "الأدب الإسلامي",
    title: language === 'en' ? "Discover a World of Knowledge" : language === 'fr' ? "Découvrir un Monde de Connaissances" : "اكتشف عالماً من المعرفة",
    description: language === 'en' ? "Explore our curated collection of books and Quran editions, bringing wisdom and knowledge to your life." :
                language === 'fr' ? "Explorez notre collection de livres et d'éditions du Coran, apportant sagesse et connaissance à votre vie." :
                "استكشف مجموعتنا المختارة من الكتب وإصدارات القرآن، لتضيف الحكمة والمعرفة إلى حياتك.",
    image: "/kotobcom.png"
  };

  // Trigger animations after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="hero-container relative overflow-hidden bg-[#6e191c] dark:bg-gradient-to-br dark:from-[#050505] dark:via-[#0a0a0a] dark:to-[#050505] w-full max-w-full mx-0 px-0 border-0 outline-0">
      {/* 🌙 Ultra Premium Dark Background */}
      <div className="absolute inset-0 bg-[#6e191c] dark:bg-gradient-to-br dark:from-[#050505] dark:via-[#0a0a0a] dark:to-[#050505]"></div>

      {/* 💫 Enhanced Pattern Overlay */}
      <div className="absolute inset-0 opacity-10 dark:opacity-5 bg-grid-pattern"></div>

      {/* 🔥 Premium Accent Lines */}
      <div className="absolute top-0 left-0 w-full h-[2px] bg-white/10 dark:bg-gradient-to-r dark:from-transparent dark:via-[#6e191c]/60 dark:to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-[2px] bg-black/10 dark:bg-gradient-to-r dark:from-transparent dark:via-[#6e191c]/60 dark:to-transparent"></div>

      {/* Enhanced burgundy circular accent for dark mode */}
      <div className="hidden dark:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[520px] h-[520px] rounded-full bg-[#6e191c] opacity-95"></div>

      {/* Enhanced glow effect around the burgundy circle */}
      <div className="hidden dark:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[550px] h-[550px] rounded-full bg-[#6e191c]/40 blur-2xl"></div>

      {/* Additional subtle radial gradient for depth */}
      <div className="hidden dark:block absolute inset-0 bg-radial-gradient from-transparent via-transparent to-black/70 opacity-80"></div>

      {/* Enhanced main content with increased spacing */}
      <div className="relative z-10 py-20 md:py-28 lg:py-36">
        <div className="container mx-auto px-4 md:px-8">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-10 md:gap-8 items-center">

            {/* Image section - larger and more prominent */}
            <div className="md:col-span-5 order-2 md:order-1 flex justify-center">
              <div className={`relative transition-all duration-700 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                   style={{maxWidth: "320px"}}>
                {/* Image container with circular border */}
                <div className="relative rounded-full overflow-hidden bg-[#6e191c] dark:bg-transparent p-4 border-[8px] border-white dark:border-transparent shadow-xl dark:shadow-none">
                  <div className="relative rounded-full overflow-hidden aspect-square">
                    <img
                      src={heroContent.image}
                      alt="Kotobcom Logo"
                      className="w-full h-full object-cover relative z-10"
                      loading="eager"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://placehold.co/400x400?text=Kotobcom';
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Text content - larger and more impactful */}
            <div className={`md:col-span-7 order-1 md:order-2 ${isRTL ? 'text-right' : ''}`}>
              {/* Enhanced tag badge */}
              <div className={`inline-flex items-center mb-5 transition-all duration-700 delay-100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
                <div className="px-5 py-2 text-sm font-medium tracking-wider rounded-md
                  bg-white/90 text-[#6e191c] dark:bg-[#080808] dark:text-[#ded7ba] border border-white/80 dark:border-[#6e191c]/40 shadow-md dark:shadow-lg relative overflow-hidden group">
                  {/* Subtle shine effect for dark mode */}
                  <div className="hidden dark:block absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-[#6e191c]/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                  <span className="uppercase letter-spacing-wide relative z-10">{heroContent.tag}</span>
                </div>
              </div>

              {/* Enhanced heading with larger typography */}
              <h1 className={`text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6 leading-tight
                text-white dark:text-[#ded7ba] tracking-tight transition-all duration-700 delay-150 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
                {heroContent.title}
              </h1>

              {/* Enhanced paragraph with better readability */}
              <p className={`text-lg md:text-xl mb-8 text-white/90 dark:text-[#ded7ba]/90 max-w-xl
                leading-relaxed transition-all duration-700 delay-200 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
                {heroContent.description}
              </p>

              {/* Luxurious buttons with enhanced interaction design */}
              <div className={`flex flex-wrap gap-5 transition-all duration-700 delay-250 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
                <Link to="/books">
                  <Button size="lg" className="bg-white hover:bg-white/90 text-[#6e191c] dark:bg-[#080808] dark:hover:bg-[#0d0d0d] dark:text-[#ded7ba]
                    font-medium text-base rounded-md px-7 py-3.5 shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group relative overflow-hidden">
                    {/* Enhanced shine effect for dark mode */}
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 dark:via-[#6e191c]/20"></div>

                    <div className="flex items-center relative z-10">
                      <BookOpen className="h-5 w-5 mr-3 text-[#6e191c] dark:text-[#6e191c] transition-transform duration-300 group-hover:scale-110" />
                      <span className="text-[#6e191c] dark:text-[#ded7ba] font-medium">{t('exploreBooks')}</span>
                      {!isRTL
                        ? <ChevronRight className="ml-2 h-4 w-4 text-[#6e191c] dark:text-[#6e191c] transition-transform duration-300 group-hover:translate-x-1" />
                        : <ChevronRight className="mr-2 h-4 w-4 text-[#6e191c] dark:text-[#6e191c] rotate-180 transition-transform duration-300 group-hover:-translate-x-1" />}
                    </div>

                    {/* Enhanced glow effect for dark mode */}
                    <div className="hidden dark:block absolute -inset-1 bg-[#6e191c]/30 rounded-md blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Border glow for dark mode */}
                    <div className="hidden dark:block absolute inset-0 rounded-md border border-[#6e191c]/40 group-hover:border-[#6e191c]/60 transition-colors duration-300"></div>
                  </Button>
                </Link>
                <Link to="/cart">
                  <Button size="lg" className="bg-[#6e191c] hover:bg-[#8a1e24] dark:bg-[#6e191c] dark:hover:bg-[#8a1e24] text-white dark:text-[#ded7ba]
                    font-medium text-base rounded-md px-7 py-3.5 shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group">
                    {/* Enhanced shine effect */}
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 dark:via-[#ded7ba]/10"></div>

                    <div className="flex items-center relative z-10">
                      <ShoppingCart className="h-5 w-5 mr-3 text-white dark:text-[#ded7ba] transition-transform duration-300 group-hover:scale-110" />
                      <span className="text-white dark:text-[#ded7ba] font-medium">{t('viewCart')}</span>
                    </div>

                    {/* Enhanced glow effect for dark mode */}
                    <div className="hidden dark:block absolute -inset-1 bg-[#6e191c]/40 rounded-md blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Subtle border for dark mode */}
                    <div className="hidden dark:block absolute inset-0 rounded-md border border-[#6e191c]/50"></div>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;


