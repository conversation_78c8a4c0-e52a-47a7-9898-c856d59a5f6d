import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { ChevronDown } from 'lucide-react';

interface HeroSeparatorProps {
  scrollToContent?: () => void;
}

const HeroSeparator: React.FC<HeroSeparatorProps> = ({ scrollToContent }) => {
  const { theme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (scrollToContent) {
      scrollToContent();
    } else {
      // Default scroll behavior if no function is provided
      window.scrollTo({
        top: window.innerHeight,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="relative hero-separator">
      {/* Enhanced separator for light mode */}
      <div className={`${theme === 'light' ? 'block' : 'hidden'} w-full overflow-hidden leading-none`}>
        <svg
          className="relative block w-full h-[60px] md:h-[90px]"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0 L1200,0 L1200,0 C900,40 700,120 600,120 C500,120 300,40 0,0 Z"
            className="fill-white"
          ></path>
        </svg>
      </div>

      {/* Enhanced separator for dark mode */}
      <div className={`${theme === 'dark' ? 'block' : 'hidden'} w-full overflow-hidden leading-none`}>
        <svg
          className="relative block w-full h-[60px] md:h-[90px]"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0 L1200,0 L1200,0 C900,40 700,120 600,120 C500,120 300,40 0,0 Z"
            className="fill-[#1a1a1a]"
          ></path>
        </svg>

        {/* Enhanced burgundy accent line */}
        <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/40 to-transparent"></div>
      </div>

      {/* Enhanced scroll button */}
      <div
        className="absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-1/2 z-10"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className={`
          w-12 h-12 md:w-16 md:h-16 rounded-full flex items-center justify-center cursor-pointer
          ${theme === 'light'
            ? 'bg-white text-[#6e191c]'
            : 'bg-[#2a2a2a] text-[#6e191c]'}
          shadow-lg hover:shadow-xl transition-all duration-500
          border ${theme === 'light' ? 'border-[#6e191c]/20' : 'border-[#6e191c]/30'}
          transform ${isHovered ? 'scale-110' : 'scale-100'} relative overflow-hidden
        `}
        onClick={handleClick}
        style={{
          boxShadow: isHovered
            ? theme === 'light'
              ? '0 10px 25px rgba(110,25,28,0.25)'
              : '0 10px 25px rgba(110,25,28,0.2)'
            : theme === 'light'
              ? '0 6px 15px rgba(110,25,28,0.2)'
              : '0 6px 15px rgba(0,0,0,0.25)',
          transition: 'all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)'
        }}
        >
          {/* Enhanced inner glow */}
          <div
            className={`absolute inset-0 rounded-full opacity-0 transition-opacity duration-500 ${isHovered ? 'opacity-100' : ''}`}
            style={{
              background: theme === 'light'
                ? 'radial-gradient(circle at center, rgba(110,25,28,0.1), transparent 70%)'
                : 'radial-gradient(circle at center, rgba(110,25,28,0.15), transparent 70%)'
            }}
          ></div>

          {/* Enhanced animated icon with smoother bounce */}
          <div
            className={`transition-transform duration-500 ${isHovered ? 'translate-y-[-2px]' : ''}`}
            style={{
              animation: isHovered ? 'bounce 1.5s infinite' : 'none',
              animationTimingFunction: 'cubic-bezier(0.28, 0.84, 0.42, 1)'
            }}
          >
            <ChevronDown
              className={`w-6 h-6 md:w-8 md:h-8 transition-colors duration-300 ${isHovered ? 'text-[#8a1e24]' : 'text-[#6e191c]'}`}
            />
          </div>
        </div>

        {/* Enhanced shadow beneath */}
        <div
          className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-3/4 h-3 rounded-full transition-all duration-500 ${isHovered ? 'opacity-70 scale-110' : 'opacity-50 scale-100'}`}
          style={{
            background: theme === 'light'
              ? 'radial-gradient(ellipse at center, rgba(0,0,0,0.1), transparent 70%)'
              : 'radial-gradient(ellipse at center, rgba(0,0,0,0.2), transparent 70%)'
          }}
        ></div>
      </div>

      {/* Subtle accent line for light mode */}
      {theme === 'light' && (
        <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-[#6e191c]/0 via-[#6e191c]/30 to-[#6e191c]/0"></div>
      )}
    </div>
  );
};

export default HeroSeparator;
