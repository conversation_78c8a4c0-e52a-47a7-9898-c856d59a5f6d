import { useState, useEffect, useRef } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Gift, Award, Clock, Mail } from 'lucide-react';
import Hero from '@/components/Hero';
import HeroSeparator from '@/components/HeroSeparator';
import { BookData } from '@/data/books';
import { fetchBooks, fetchQuranBooks } from '@/services/bookService';
import { useTheme } from '@/contexts/ThemeContext';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import FeaturedCategories from '@/components/FeaturedCategories';
import BookSection from '@/components/BookSection';
const Home = () => {
  const { language } = useLanguage();
  const { theme } = useTheme();
  const [featuredBooks, setFeaturedBooks] = useState<BookData[]>([]);
  const [newArrivals, setNewArrivals] = useState<BookData[]>([]);
  const [bestSellers, setBestSellers] = useState<BookData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Refs for scroll reveal animation
  const featuredRef = useRef<HTMLDivElement>(null);
  const newArrivalsRef = useRef<HTMLDivElement>(null);
  const bestSellersRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const loadBooks = async () => {
      try {
        setIsLoading(true);
        const [books, quranBooks] = await Promise.all([fetchBooks(), fetchQuranBooks()]);

        // Combine all books
        const allBooks = [...books, ...quranBooks].filter(Boolean); // Filter out any null values

        // Filter books by category
        setFeaturedBooks(allBooks.filter(book => book?.featured).slice(0, 4));
        setNewArrivals(allBooks.filter(book => book?.newArrival).slice(0, 4));
        setBestSellers(allBooks.filter(book => book?.bestSeller).slice(0, 4));
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading books:', error);
        setIsLoading(false);
      }
    };
    loadBooks();
  }, []);

  // Scroll reveal animation
  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('active');
        }
      });
    }, {
      threshold: 0.1
    });
    const refs = [featuredRef, newArrivalsRef, bestSellersRef];
    refs.forEach(ref => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });
    return () => {
      refs.forEach(ref => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, []);

  // Reference to the content section for smooth scrolling
  const contentRef = useRef<HTMLDivElement>(null);

  const scrollToContent = () => {
    contentRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return <main className="relative">
      {/* Hero Section */}
      <div className="hero-section">
        <Hero />
        <HeroSeparator scrollToContent={scrollToContent} />
      </div>

      {/* 🌙 PREMIUM DARK MODE CONTENT SECTION */}
      <div
        ref={contentRef}
        className={`content-section relative ${theme === 'light' ? 'bg-white' : 'bg-gradient-to-b from-[#050505] via-[#0a0a0a] to-[#050505]'} transition-all duration-500`}
      >
        {/* 💫 Ultra Premium Background System */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {theme === 'dark' ? (
            <>
              {/* 🎭 Sophisticated Dark Mode Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-[#050505] via-[#0a0a0a] to-[#0f0f0f]"></div>

              {/* 🌟 Ambient Glow Effects */}
              <div className="absolute top-0 left-1/4 w-[800px] h-[400px] bg-gradient-radial from-[#6e191c]/20 via-[#6e191c]/10 to-transparent rounded-full blur-3xl opacity-60 animate-pulse-slow"></div>
              <div className="absolute bottom-0 right-1/4 w-[600px] h-[300px] bg-gradient-radial from-[#8a1e24]/15 via-[#6e191c]/8 to-transparent rounded-full blur-3xl opacity-50 animate-floating"></div>

              {/* ✨ Elegant Border Gradients */}
              <div className="absolute top-0 left-0 w-full h-[3px] bg-gradient-to-r from-transparent via-[#6e191c]/60 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-[#6e191c]/40 to-transparent"></div>

              {/* 🔮 Mystical Geometric Patterns */}
              <div className="absolute top-32 right-20 w-40 h-40 border border-[#6e191c]/20 rounded-full opacity-40 animate-spin-slow">
                <div className="absolute inset-4 border border-[#6e191c]/15 rounded-full"></div>
                <div className="absolute inset-8 border border-[#6e191c]/10 rounded-full"></div>
              </div>
              <div className="absolute bottom-40 left-16 w-32 h-32 border border-[#6e191c]/15 rounded-full opacity-30">
                <div className="absolute inset-2 bg-[#6e191c]/5 rounded-full blur-sm"></div>
              </div>
              <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-gradient-to-br from-[#6e191c]/10 to-transparent rounded-full opacity-25"></div>

              {/* 💎 Crystalline Effects */}
              <div className="absolute top-20 left-1/3 w-2 h-2 bg-[#6e191c] rounded-full opacity-60 animate-pulse"></div>
              <div className="absolute bottom-32 right-1/4 w-1 h-1 bg-[#8a1e24] rounded-full opacity-80 animate-pulse" style={{animationDelay: '1s'}}></div>
              <div className="absolute top-2/3 left-1/4 w-1.5 h-1.5 bg-[#6e191c] rounded-full opacity-70 animate-pulse" style={{animationDelay: '2s'}}></div>
            </>
          ) : (
            <>
              {/* Clean light mode background with subtle textures */}
              <div className="absolute inset-0 bg-gradient-to-b from-white via-[#fefefe] to-white"></div>
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/10 to-transparent"></div>
              {/* Minimal decorative elements */}
              <div className="absolute top-32 right-20 w-16 h-16 bg-[#6e191c]/5 rounded-full opacity-50"></div>
              <div className="absolute bottom-60 left-16 w-12 h-12 bg-[#6e191c]/5 rounded-full opacity-30"></div>
            </>
          )}
        </div>

        {/* Featured Categories Section with enhanced spacing */}
        <div className="relative z-10 pt-8">
          <FeaturedCategories />
        </div>

        {/* Enhanced Featured Books Section with improved visual hierarchy */}
        <div className="relative z-10 py-8">
          <BookSection
            title="featured"
            books={featuredBooks}
            linkTo="/books"
            icon={<Award className={`h-6 w-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} transition-all duration-300 hover:scale-110 hover:rotate-12`} />}
            isLoading={isLoading}
            ref={featuredRef}
          />
        </div>

        {/* Enhanced New Arrivals Section with improved spacing */}
        <div className="relative z-10 py-8">
          <BookSection
            title="newArrivals"
            books={newArrivals}
            linkTo="/books"
            icon={<Clock className={`h-6 w-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} transition-all duration-300 hover:scale-110 hover:rotate-12`} />}
            isLoading={isLoading}
            ref={newArrivalsRef}
          />
        </div>

        {/* Enhanced visual separator with better styling */}
        <div className="container relative z-10 py-8">
          <div className="flex items-center justify-center">
            <div className={`h-[1px] flex-1 ${theme === 'light' ? 'bg-gradient-to-r from-transparent via-[#6e191c]/20 to-transparent' : 'bg-gradient-to-r from-transparent via-[#6e191c]/30 to-transparent'}`}></div>
            <div className={`mx-6 w-2 h-2 rounded-full ${theme === 'light' ? 'bg-[#6e191c]/30' : 'bg-[#6e191c]/50'}`}></div>
            <div className={`h-[1px] flex-1 ${theme === 'light' ? 'bg-gradient-to-r from-transparent via-[#6e191c]/20 to-transparent' : 'bg-gradient-to-r from-transparent via-[#6e191c]/30 to-transparent'}`}></div>
          </div>
        </div>

        {/* Enhanced Best Sellers Section with improved visual appeal */}
        <div className="relative z-10 py-8">
          <BookSection
            title="bestSellers"
            books={bestSellers}
            linkTo="/books"
            icon={<Gift className={`h-6 w-6 ${theme === 'light' ? 'text-[#6e191c]' : 'text-[#6e191c]'} transition-all duration-300 hover:scale-110 hover:rotate-12`} />}
            isLoading={isLoading}
            ref={bestSellersRef}
          />
        </div>

      {/* 🌙 ULTRA PREMIUM NEWSLETTER SECTION */}
      <section className={`py-24 relative overflow-hidden ${theme === 'light' ? 'bg-gradient-to-b from-white to-[#fafafa]' : 'bg-gradient-to-br from-[#050505] via-[#0a0a0a] to-[#050505]'}`}>
        {/* 💫 Spectacular Background System */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {theme === 'dark' ? (
            <>
              {/* 🌟 Majestic Glow Effects */}
              <div className="absolute -bottom-40 right-0 w-[80rem] h-[80rem] rounded-full bg-gradient-radial from-[#6e191c]/25 via-[#6e191c]/15 to-transparent blur-3xl opacity-70 animate-pulse-slow"></div>
              <div className="absolute -top-40 left-0 w-[70rem] h-[70rem] rounded-full bg-gradient-radial from-[#8a1e24]/20 via-[#6e191c]/10 to-transparent blur-3xl opacity-60 animate-floating"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60rem] h-[60rem] rounded-full bg-gradient-radial from-[#6e191c]/8 to-transparent blur-3xl opacity-40"></div>

              {/* 🎭 Sophisticated Geometric Patterns */}
              <div className="absolute top-24 right-24 w-48 h-48 border border-[#6e191c]/25 rounded-full opacity-50 animate-spin-slow">
                <div className="absolute inset-6 border border-[#6e191c]/20 rounded-full animate-pulse">
                  <div className="absolute inset-6 border border-[#6e191c]/15 rounded-full">
                    <div className="absolute inset-6 bg-gradient-to-br from-[#6e191c]/10 to-transparent rounded-full"></div>
                  </div>
                </div>
              </div>
              <div className="absolute bottom-32 left-20 w-36 h-36 border border-[#6e191c]/20 rounded-full opacity-40">
                <div className="absolute inset-4 bg-gradient-to-br from-[#6e191c]/15 to-transparent rounded-full blur-sm"></div>
              </div>
              <div className="absolute top-1/3 right-1/4 w-28 h-28 bg-gradient-to-br from-[#6e191c]/12 to-transparent rounded-full opacity-35 animate-floating" style={{animationDelay: '1s'}}></div>

              {/* ✨ Mystical Light Particles */}
              <div className="absolute top-20 left-1/4 w-3 h-3 bg-[#6e191c] rounded-full opacity-70 animate-pulse shadow-lg shadow-[#6e191c]/50"></div>
              <div className="absolute bottom-40 right-1/3 w-2 h-2 bg-[#8a1e24] rounded-full opacity-80 animate-pulse shadow-lg shadow-[#8a1e24]/50" style={{animationDelay: '1.5s'}}></div>
              <div className="absolute top-2/3 left-1/3 w-2.5 h-2.5 bg-[#6e191c] rounded-full opacity-75 animate-pulse shadow-lg shadow-[#6e191c]/50" style={{animationDelay: '2.5s'}}></div>
              <div className="absolute top-1/4 right-1/5 w-1.5 h-1.5 bg-[#a52229] rounded-full opacity-85 animate-pulse shadow-lg shadow-[#a52229]/50" style={{animationDelay: '3s'}}></div>

              {/* 🔥 Elegant Border Gradients */}
              <div className="absolute top-0 left-0 w-full h-[3px] bg-gradient-to-r from-transparent via-[#6e191c]/50 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[3px] bg-gradient-to-r from-transparent via-[#6e191c]/50 to-transparent"></div>

              {/* 💎 Crystalline Grid Pattern */}
              <div className="absolute inset-0 opacity-5" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, #6e191c 1px, transparent 0)`,
                backgroundSize: '40px 40px'
              }}></div>
            </>
          ) : (
            <>
              {/* Clean light mode background effects */}
              <div className="absolute -bottom-20 right-0 w-[40rem] h-[40rem] rounded-full bg-[#6e191c]/5 blur-3xl opacity-40"></div>
              <div className="absolute -top-20 left-0 w-[35rem] h-[35rem] rounded-full bg-[#6e191c]/3 blur-3xl opacity-30"></div>

              {/* Minimal decorative elements */}
              <div className="absolute top-16 right-16 w-20 h-20 bg-[#6e191c]/5 rounded-full opacity-40"></div>
              <div className="absolute bottom-20 left-12 w-16 h-16 bg-[#6e191c]/5 rounded-full opacity-30"></div>

              {/* Subtle borders */}
              <div className="absolute top-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/15 to-transparent"></div>
              <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-[#6e191c]/15 to-transparent"></div>
            </>
          )}
        </div>

        <div className="container relative z-10">
          <div className={`rounded-3xl overflow-hidden backdrop-blur-xl transition-all duration-500 hover:scale-[1.03] group
            ${theme === 'light'
              ? 'bg-white/95 border border-[#6e191c]/15 shadow-[0_25px_60px_rgba(110,25,28,0.12)] hover:shadow-[0_35px_80px_rgba(110,25,28,0.18)]'
              : 'bg-gradient-to-br from-[#0a0a0a]/95 via-[#0f0f0f]/90 to-[#0a0a0a]/95 border border-[#6e191c]/40 shadow-[0_30px_80px_rgba(0,0,0,0.8),0_15px_40px_rgba(110,25,28,0.3)] hover:shadow-[0_40px_120px_rgba(0,0,0,0.9),0_20px_60px_rgba(110,25,28,0.5)]'}`}
          >
            {/* 🌟 Premium Dark Mode Glow Effect */}
            {theme === 'dark' && (
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-[#6e191c]/20 via-transparent to-[#8a1e24]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            )}
            <div className="grid md:grid-cols-2 gap-0">
              {/* Enhanced Image Side with luxurious overlay */}
              <div className="relative h-72 md:h-auto overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1519682337058-a94d519337bc?ixlib=rb-4.0.3&auto=format&fit=crop&q=80"
                  alt="Books on shelf"
                  className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
                />
                <div className={`absolute inset-0 transition-all duration-500
                  ${theme === 'light'
                    ? 'bg-gradient-to-r from-[#6e191c]/40 via-[#6e191c]/20 to-transparent'
                    : 'bg-gradient-to-br from-[#050505]/98 via-[#0a0a0a]/85 to-[#0f0f0f]/70'}`}
                ></div>

                {/* 💎 Ultra Premium Decorative Elements */}
                <div className="absolute bottom-8 left-8 flex items-center group/icon">
                  <div className={`w-18 h-18 rounded-full flex items-center justify-center transition-all duration-500 group-hover/icon:scale-125 group-hover/icon:rotate-12 relative overflow-hidden
                    ${theme === 'light'
                      ? 'bg-white/90 text-[#6e191c] shadow-[0_8px_25px_rgba(110,25,28,0.2)] backdrop-blur-sm'
                      : 'bg-gradient-to-br from-[#6e191c] to-[#8a1e24] text-[#f0ebe0] shadow-[0_12px_40px_rgba(110,25,28,0.6),0_4px_16px_rgba(0,0,0,0.8)] backdrop-blur-sm border border-[#6e191c]/30'}`}
                  >
                    {/* ✨ Shine Effect for Dark Mode */}
                    {theme === 'dark' && (
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent opacity-0 group-hover/icon:opacity-100 transition-opacity duration-500"></div>
                    )}
                    <Mail className="h-8 w-8 relative z-10" />
                  </div>
                  <div className={`ml-5 pl-5 border-l-2 transition-all duration-500
                    ${theme === 'light'
                      ? 'border-white/60 text-white'
                      : 'border-[#f0ebe0]/50 text-[#f0ebe0]'}`}
                  >
                    <p className={`text-lg font-bold tracking-wide ${theme === 'dark' ? 'text-[#f0ebe0]' : 'text-white'}`}>Newsletter</p>
                    <p className={`text-sm font-medium ${theme === 'dark' ? 'text-[#f0ebe0]/80' : 'text-white/90'}`}>Stay Updated</p>
                  </div>
                </div>
              </div>

              {/* 🌟 Ultra Premium Content Side */}
              <div className="p-12 md:p-20 flex flex-col justify-center relative">
                {/* 💫 Subtle Background Glow for Dark Mode */}
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-[#6e191c]/5 via-transparent to-[#8a1e24]/5 rounded-r-3xl"></div>
                )}

                <Badge className={`mb-8 px-6 py-3 text-sm font-bold transition-all duration-500 hover:scale-110 hover:shadow-2xl relative z-10
                  ${theme === 'light'
                    ? 'bg-[#6e191c]/12 text-[#6e191c] border border-[#6e191c]/25 shadow-[0_4px_15px_rgba(110,25,28,0.1)]'
                    : 'bg-gradient-to-r from-[#6e191c]/25 to-[#8a1e24]/20 text-[#f0ebe0] border border-[#6e191c]/50 shadow-[0_8px_30px_rgba(110,25,28,0.4)] backdrop-blur-sm'}`}
                >
                  {language === 'en' ? "Stay Updated" : language === 'fr' ? "Restez Informé" : "ابق على اطلاع"}
                </Badge>

                <h2 className={`text-4xl md:text-5xl font-serif font-bold mb-8 tracking-tight leading-tight relative z-10
                  ${theme === 'light'
                    ? 'text-[#6e191c]'
                    : 'text-[#f0ebe0] drop-shadow-[0_4px_8px_rgba(0,0,0,0.8)]'}`}
                >
                  {language === 'en' ? "Subscribe to Our Newsletter" :
                   language === 'fr' ? "Abonnez-vous à Notre Newsletter" :
                   "اشترك في نشرتنا الإخبارية"}
                </h2>

                {/* 💎 Premium Decorative Underline */}
                <div className={`h-2 w-32 rounded-full mb-10 transition-all duration-500 relative
                  ${theme === 'light'
                    ? 'bg-gradient-to-r from-[#6e191c]/30 to-[#6e191c]/10'
                    : 'bg-gradient-to-r from-[#6e191c] via-[#8a1e24] to-[#6e191c] shadow-[0_4px_16px_rgba(110,25,28,0.6)]'}`}
                >
                  {theme === 'dark' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-[#6e191c] via-[#8a1e24] to-[#6e191c] rounded-full blur-sm opacity-50"></div>
                  )}
                </div>

                <p className={`mb-12 leading-relaxed text-xl relative z-10
                  ${theme === 'light'
                    ? 'text-black/75'
                    : 'text-[#f0ebe0]/90 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]'}`}
                >
                  {language === 'en' ? "Get notified about new arrivals, special offers, and exclusive events. Join our community of book lovers." :
                   language === 'fr' ? "Soyez informé des nouveautés, des offres spéciales et des événements exclusifs. Rejoignez notre communauté d'amoureux des livres." :
                   "احصل على إشعارات حول الوصول الجديد والعروض الخاصة والأحداث الحصرية. انضم إلى مجتمعنا من محبي الكتب."}
                </p>

                <div className="flex flex-col sm:flex-row gap-6 relative z-10">
                  <div className="relative flex-grow group">
                    <Mail className={`absolute left-6 top-1/2 transform -translate-y-1/2 h-6 w-6 transition-all duration-500 group-hover:scale-125 group-hover:rotate-12
                      ${theme === 'light'
                        ? 'text-[#6e191c]/70 group-hover:text-[#6e191c]'
                        : 'text-[#6e191c] group-hover:text-[#8a1e24] drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]'}`}
                    />
                    <input
                      type="email"
                      placeholder={language === 'en' ? "Your email address" : language === 'fr' ? "Votre adresse email" : "عنوان بريدك الإلكتروني"}
                      className={`w-full pl-16 pr-6 py-5 rounded-2xl transition-all duration-500 text-lg font-medium
                        ${theme === 'light'
                          ? 'bg-white border-[#6e191c]/25 text-[#6e191c] placeholder:text-[#6e191c]/60 focus:border-[#6e191c]/50 focus:ring-[#6e191c]/15 shadow-[0_4px_15px_rgba(110,25,28,0.08)]'
                          : 'bg-gradient-to-br from-[#0f0f0f] to-[#141414] border-[#6e191c]/50 text-[#f0ebe0] placeholder:text-[#f0ebe0]/60 focus:border-[#6e191c] focus:ring-[#6e191c]/30 shadow-[0_8px_30px_rgba(0,0,0,0.8),0_4px_16px_rgba(110,25,28,0.3)] backdrop-blur-sm'}
                        border-2 focus:outline-none focus:ring-4 hover:shadow-2xl focus:shadow-2xl`}
                    />
                    {/* 💫 Input Glow Effect for Dark Mode */}
                    {theme === 'dark' && (
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#6e191c]/20 via-transparent to-[#8a1e24]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                    )}
                  </div>
                  <Button className={`py-5 px-12 rounded-2xl font-bold text-lg transition-all duration-500 relative overflow-hidden group
                    ${theme === 'light'
                      ? 'bg-[#6e191c] hover:bg-[#8a1e24] text-white shadow-[0_8px_25px_rgba(110,25,28,0.25)] hover:shadow-[0_12px_35px_rgba(110,25,28,0.35)] hover:-translate-y-1 hover:scale-105'
                      : 'bg-gradient-to-r from-[#6e191c] to-[#8a1e24] hover:from-[#8a1e24] hover:to-[#a52229] text-[#f0ebe0] shadow-[0_12px_40px_rgba(110,25,28,0.5),0_4px_16px_rgba(0,0,0,0.8)] hover:shadow-[0_16px_60px_rgba(110,25,28,0.7),0_8px_32px_rgba(0,0,0,0.9)] hover:-translate-y-2 hover:scale-110'}`}
                  >
                    {/* 🌟 Ultra Premium Shine Effect */}
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1200"></div>

                    {/* 💎 Glow Effect for Dark Mode */}
                    {theme === 'dark' && (
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#6e191c] to-[#8a1e24] blur-lg opacity-0 group-hover:opacity-50 transition-opacity duration-500"></div>
                    )}

                    <span className="relative z-10 tracking-wide">
                      {language === 'en' ? "Subscribe" : language === 'fr' ? "S'abonner" : "اشترك"}
                    </span>
                  </Button>
                </div>

                <p className={`mt-6 text-sm leading-relaxed
                  ${theme === 'light'
                    ? 'text-black/60'
                    : 'text-white/70'}`}
                >
                  {language === 'en' ? "By subscribing, you agree to our Privacy Policy and consent to receive updates from our company." :
                   language === 'fr' ? "En vous abonnant, vous acceptez notre politique de confidentialité et consentez à recevoir des mises à jour de notre entreprise." :
                   "بالاشتراك، فإنك توافق على سياسة الخصوصية الخاصة بنا وتوافق على تلقي تحديثات من شركتنا."}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      </div>
    </main>
};
export default Home;